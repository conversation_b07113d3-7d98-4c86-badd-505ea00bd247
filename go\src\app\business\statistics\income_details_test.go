package statistics

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
)

// TestGetIncomeDetailsList 测试获取收入明细列表
func TestGetIncomeDetailsList(t *testing.T) {
	// 创建服务实例
	ctx := context.Background()
	service := NewService(ctx)

	// 测试参数
	params := map[string]interface{}{
		"page":      1,
		"page_size": 10,
	}

	// 执行测试
	result, err := service.GetIncomeDetailsList(params)

	// 验证结果
	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.NotNil(t, result.Statistics)
	assert.NotNil(t, result.List)
}

// TestDeterminePaymentMethod 测试收款方式判断
func TestDeterminePaymentMethod(t *testing.T) {
	tests := []struct {
		name     string
		data     map[string]interface{}
		expected string
	}{
		{
			name: "线下支付",
			data: map[string]interface{}{
				"offline_payment_channel_detail": "支付宝支付（线下）",
				"type":                           "REPAYMENT",
				"withhold_type":                  "",
			},
			expected: "线下支付",
		},
		{
			name: "资管支付",
			data: map[string]interface{}{
				"offline_payment_channel_detail": "",
				"type":                           "REPAYMENT",
				"withhold_type":                  "ASSET",
			},
			expected: "资管支付",
		},
		{
			name: "资管代扣",
			data: map[string]interface{}{
				"offline_payment_channel_detail": "",
				"type":                           "WITHHOLD",
				"withhold_type":                  "ASSET",
			},
			expected: "资管代扣",
		},
		{
			name: "担保支付",
			data: map[string]interface{}{
				"offline_payment_channel_detail": "",
				"type":                           "REPAYMENT",
				"withhold_type":                  "GUARANTEE",
			},
			expected: "担保支付",
		},
		{
			name: "担保代扣",
			data: map[string]interface{}{
				"offline_payment_channel_detail": "",
				"type":                           "WITHHOLD",
				"withhold_type":                  "GUARANTEE",
			},
			expected: "担保代扣",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := DeterminePaymentMethod(tt.data)
			assert.Equal(t, tt.expected, result)
		})
	}
}

// TestDetermineFundType 测试款项类型判断
func TestDetermineFundType(t *testing.T) {
	tests := []struct {
		name        string
		paymentType string
		expected    string
	}{
		{
			name:        "退款",
			paymentType: "REFUND",
			expected:    "退款",
		},
		{
			name:        "收款",
			paymentType: "REPAYMENT",
			expected:    "收款",
		},
		{
			name:        "代扣",
			paymentType: "WITHHOLD",
			expected:    "收款",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := DetermineFundType(tt.paymentType)
			assert.Equal(t, tt.expected, result)
		})
	}
}
