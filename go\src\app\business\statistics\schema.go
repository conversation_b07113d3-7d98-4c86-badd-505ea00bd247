package statistics

import "fincore/utils/jsonschema"

// GetChannelStatisticsSchema 渠道统计列表查询参数验证规则
func GetChannelStatisticsSchema() jsonschema.Schema {
	return jsonschema.Schema{
		Title: "渠道统计列表查询参数",
		Type:  "object",
		Properties: map[string]jsonschema.ValidateRule{
			"date": {
				Type:        "string",
				Required:    false,
				Pattern:     "^\\d{4}-\\d{2}-\\d{2}$",
				Description: "统计日期（YYYY-MM-DD格式，可选）",
			},
			"page": {
				Type:        "number",
				Required:    false,
				Description: "页码",
				Default:     1,
			},
			"page_size": {
				Type:        "number",
				Required:    false,
				Description: "每页数量",
				Default:     10,
			},
		},
		Required: []string{},
	}
}

// GetHomeStatisticsSchema 首页数据统计查询参数验证规则
func GetHomeStatistics() jsonschema.Schema {
	return jsonschema.Schema{
		Title: "首页数据统计查询参数",
		Type:  "object",
		Properties: map[string]jsonschema.ValidateRule{
			"date_begin": {
				Type:        "string",
				Required:    false,
				Pattern:     "^\\d{4}-\\d{2}-\\d{2}$",
				Description: "统计日期（YYYY-MM-DD格式，可选）",
			},
			"date_end": {
				Type:        "string",
				Required:    false,
				Pattern:     "^\\d{4}-\\d{2}-\\d{2}$",
				Description: "统计日期（YYYY-MM-DD格式，可选）",
			},
		},
		Required: []string{},
	}
}

// GetTrendStatisticsSchema 趋势统计查询参数验证规则
func GetTrendStatisticsSchema() jsonschema.Schema {
	return jsonschema.Schema{
		Title: "趋势统计查询参数",
		Type:  "object",
		Properties: map[string]jsonschema.ValidateRule{
			"days": {
				Type:        "number",
				Required:    true,
				Enum:        []string{"7", "30"},
				Description: "查询天数，支持7日或30日",
			},
		},
		Required: []string{"days"},
	}
}

// GetIncomeDetailsSchema 收入明细查询参数验证规则
func GetIncomeDetailsSchema() jsonschema.Schema {
	return jsonschema.Schema{
		Title: "收入明细查询参数",
		Type:  "object",
		Properties: map[string]jsonschema.ValidateRule{
			"order_no": {
				Type:        "string",
				Required:    false,
				Description: "订单编号",
			},
			"user_name": {
				Type:        "string",
				Required:    false,
				Description: "用户姓名",
			},
			"mobile": {
				Type:        "string",
				Required:    false,
				Pattern:     "^1[3-9]\\d{9}$",
				Description: "手机号",
			},
			"fund_type": {
				Type:        "string",
				Required:    false,
				Enum:        []string{"收款", "退款"},
				Description: "款项类型",
			},
			"payment_method": {
				Type:        "string",
				Required:    false,
				Enum:        []string{"资管支付", "资管代扣", "担保支付", "担保代扣", "线下支付"},
				Description: "收款方式",
			},
			"payment_status": {
				Type:        "string",
				Required:    false,
				Enum:        []string{"提前收款", "到期收款", "逾期收款"},
				Description: "收款状态",
			},
			"payment_time_start": {
				Type:        "string",
				Required:    false,
				Pattern:     "^\\d{4}-\\d{2}-\\d{2}$",
				Description: "收款开始时间（YYYY-MM-DD）",
			},
			"payment_time_end": {
				Type:        "string",
				Required:    false,
				Pattern:     "^\\d{4}-\\d{2}-\\d{2}$",
				Description: "收款结束时间（YYYY-MM-DD）",
			},
			"bill_time_start": {
				Type:        "string",
				Required:    false,
				Pattern:     "^\\d{4}-\\d{2}-\\d{2}$",
				Description: "账单开始时间（YYYY-MM-DD）",
			},
			"bill_time_end": {
				Type:        "string",
				Required:    false,
				Pattern:     "^\\d{4}-\\d{2}-\\d{2}$",
				Description: "账单结束时间（YYYY-MM-DD）",
			},
			"page": {
				Type:        "number",
				Required:    false,
				Description: "页码",
				Default:     1,
			},
			"page_size": {
				Type:        "number",
				Required:    false,
				Description: "每页数量",
				Default:     10,
			},
		},
		Required: []string{},
	}
}
