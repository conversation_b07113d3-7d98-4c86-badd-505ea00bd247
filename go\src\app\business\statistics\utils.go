package statistics

import (
	"time"

	"fincore/utils/gform"

	"github.com/gogf/gf/v2/util/gconv"
	"github.com/golang-module/carbon/v2"
)

// GetTodayTimeRange 获取今天的时间范围
// 返回今天的开始时间和结束时间
func GetTodayTimeRange() (startTime, endTime time.Time) {
	todayStart := carbon.Now().StartOfDay()
	todayEnd := carbon.Now().EndOfDay()

	return todayStart.StdTime(), todayEnd.StdTime()
}

// GetYesterdayTimeRange 获取昨天的时间范围
// 返回昨天的开始时间和结束时间
func GetYesterdayTimeRange() (startTime, endTime time.Time) {
	yesterday := carbon.Now().SubDay().StartOfDay()
	today := yesterday.AddDay()

	return yesterday.StdTime(), today.StdTime()
}

// GetSpecificDateTimeRange 获取指定日期的时间范围
// 参数: date - 指定日期
// 返回指定日期的开始时间和结束时间
func GetSpecificDateTimeRange(date time.Time) (startTime, endTime time.Time) {
	specificDay := carbon.CreateFromStdTime(date).StartOfDay()
	nextDay := specificDay.AddDay()

	return specificDay.StdTime(), nextDay.StdTime()
}

// ConvertChannelData 转换渠道数据
// 将map[string]interface{}转换为ChannelInfo结构体
func ConvertChannelData(data map[string]interface{}) (*ChannelInfo, error) {
	var channelInfo ChannelInfo
	err := gconv.Struct(data, &channelInfo)
	if err != nil {
		return nil, err
	}
	return &channelInfo, nil
}

// FormatTimeForLog 格式化时间用于日志输出
func FormatTimeForLog(t time.Time) string {
	return carbon.CreateFromStdTime(t).Format("Y-m-d H:i:s")
}

// IsToday 判断给定时间是否为今天
func IsToday(t time.Time) bool {
	return carbon.CreateFromStdTime(t).IsToday()
}

// IsYesterday 判断给定时间是否为昨天
func IsYesterday(t time.Time) bool {
	return carbon.CreateFromStdTime(t).IsYesterday()
}

// GetCurrentHour 获取当前小时数
func GetCurrentHour() int {
	return carbon.Now().Hour()
}

// IsExecutionTime 判断是否为执行时间（12点或24点）
func IsExecutionTime() bool {
	hour := GetCurrentHour()
	return hour == 12 || hour == 0 // 0点表示24点
}

// GetTimeRangeDescription 获取时间范围描述
func GetTimeRangeDescription(startTime, endTime time.Time) string {
	start := carbon.CreateFromStdTime(startTime)
	end := carbon.CreateFromStdTime(endTime)

	return start.Format("Y-m-d H:i:s") + " 至 " + end.Format("Y-m-d H:i:s")
}

// ConvertUnixToTime 将Unix时间戳转换为time.Time
func ConvertUnixToTime(timestamp int64) time.Time {
	return carbon.CreateFromTimestamp(timestamp).StdTime()
}

// ConvertTimeToUnix 将time.Time转换为Unix时间戳
func ConvertTimeToUnix(t time.Time) int64 {
	return carbon.CreateFromStdTime(t).Timestamp()
}

// DeterminePaymentMethod 判断收款方式
func DeterminePaymentMethod(data gform.Data) string {
	// 线下支付
	if offlineDetail, exists := data["offline_payment_channel_detail"]; exists && offlineDetail != "" {
		return "线下支付"
	}

	// 获取字段值
	paymentType := gconv.String(data["type"])
	withholdType := gconv.String(data["withhold_type"])

	// 资管相关
	if withholdType == "ASSET" {
		if paymentType == "REPAYMENT" {
			return "资管支付"
		} else if paymentType == "WITHHOLD" || paymentType == "MANUAL_WITHHOLD" {
			return "资管代扣"
		}
	}

	// 担保相关
	if withholdType == "GUARANTEE" {
		if paymentType == "REPAYMENT" {
			return "担保支付"
		} else if paymentType == "WITHHOLD" || paymentType == "MANUAL_WITHHOLD" {
			return "担保代扣"
		}
	}

	return "其他"
}

// DeterminePaymentStatus 判断收款状态
func DeterminePaymentStatus(data gform.Data) string {
	completedAt, completedExists := data["completed_at"]
	dueDate, dueDateExists := data["due_date"]

	if !completedExists || !dueDateExists {
		return "未知"
	}

	completedTime, ok1 := completedAt.(time.Time)
	dueDateTime, ok2 := dueDate.(time.Time)

	if !ok1 || !ok2 {
		return "未知"
	}

	// 比较日期（忽略时间部分）
	completedDate := completedTime.Format("2006-01-02")
	dueDateStr := dueDateTime.Format("2006-01-02")

	if completedDate < dueDateStr {
		return "提前收款"
	} else if completedDate == dueDateStr {
		return "到期收款"
	} else {
		return "逾期收款"
	}
}

// DetermineFundType 判断款项类型
func DetermineFundType(paymentType string) string {
	if paymentType == "REFUND" {
		return "退款"
	}
	return "收款"
}
