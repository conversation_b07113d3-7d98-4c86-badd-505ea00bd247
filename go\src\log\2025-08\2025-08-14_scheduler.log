{"level":"dev.info","ts":"[2025-08-14 09:36:24.799]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-14 09:36:24.801]","caller":"scheduler/manager.go:106","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler","timezone":"Asia/Shanghai","max_concurrent_jobs":10}
{"level":"dev.info","ts":"[2025-08-14 09:36:24.801]","caller":"scheduler/scheduler.go:224","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-14 09:36:24.801]","caller":"scheduler/scheduler.go:233","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-14 09:36:24.802]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":9}
{"level":"dev.info","ts":"[2025-08-14 09:36:24.803]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-disbursement-compensation","description":"自动放款补偿任务 - 处理创建订单后自动放款异常或失败的补偿","schedule":"0 * * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:36:24.803]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-disbursement-compensation","task_type":"orders.AutoDisbursementCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 09:36:24.803]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"disbursement-status-sync-compensation","description":"放款状态同步补偿任务 - 处理订单状态为待放款但已有已提交放款流水的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:36:24.803]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"disbursement-status-sync-compensation","task_type":"orders.DisbursementStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 09:36:24.803]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"refund-status-sync-compensation","description":"退款状态同步补偿任务 - 处理已提交退款流水的状态同步","schedule":"0 */1 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:36:24.803]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"refund-status-sync-compensation","task_type":"orders.RefundStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 09:36:24.803]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-withhold-task","description":"系统自动代扣任务 - 自动处理待代扣账单","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton","timeout":1800,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:36:24.803]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-withhold-task","task_type":"repayment.AutoWithholdTask"}
{"level":"dev.info","ts":"[2025-08-14 09:36:24.803]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"repayment-status-sync-compensation","description":"还款状态同步补偿任务 - 处理还款账单状态与支付流水状态不一致的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:36:24.803]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"repayment-status-sync-compensation","task_type":"repayment.RepaymentStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 09:36:24.803]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-ahead","description":"账单到期提前通知任务 - 处理账单到期前一天，短信通知客户","schedule":"0 0 9 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:36:24.803]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-ahead","task_type":"notice.BillNoticeAheadTask"}
{"level":"dev.info","ts":"[2025-08-14 09:36:24.803]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-overdue","description":"账单逾期通知任务","schedule":"0 0 10 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:36:24.803]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-overdue","task_type":"notice.BillNoticeOverdueTask"}
{"level":"dev.info","ts":"[2025-08-14 09:36:24.803]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"available_quota_notice","description":"额度未使用通知任务","schedule":"0 0 11 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:36:24.803]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"available_quota_notice","task_type":"notice.AvailableQuotaNoticeTask"}
{"level":"dev.info","ts":"[2025-08-14 09:36:24.803]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"channel_statistics_task","description":"渠道统计任务：统计各渠道的新用户数、实名通过数、成交数","schedule":"0 0 */2 * * *","concurrency_mode":"singleton","timeout":600,"retry_count":2}
{"level":"dev.info","ts":"[2025-08-14 09:36:24.803]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"channel_statistics_task","task_type":"statistics.ChannelStatisticsTask"}
{"level":"dev.info","ts":"[2025-08-14 09:36:24.803]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":9,"success":9,"failure":0}
{"level":"dev.info","ts":"[2025-08-14 09:36:24.803]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-14 09:36:24.803]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-14 09:36:24.803]","caller":"scheduler/manager.go:124","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-14 09:36:24.803]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-08-14 09:36:24.803]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-ahead","schedule":"0 0 9 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:36:24.803]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-overdue","schedule":"0 0 10 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:36:24.803]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"disbursement-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:36:24.803]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"available_quota_notice","schedule":"0 0 11 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:36:24.803]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-withhold-task","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:36:24.804]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"channel_statistics_task","schedule":"0 0 */2 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:36:24.804]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-disbursement-compensation","schedule":"0 * * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:36:24.804]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"refund-status-sync-compensation","schedule":"0 */1 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:36:24.804]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"repayment-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:36:24.804]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":9,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-08-14 09:36:24.804]","caller":"scheduler/manager.go:139","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler","registered_tasks":9}
{"level":"dev.info","ts":"[2025-08-14 09:36:24.804]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-08-14 09:38:29.787]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-14 09:38:29.788]","caller":"scheduler/manager.go:106","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler","timezone":"Asia/Shanghai","max_concurrent_jobs":10}
{"level":"dev.info","ts":"[2025-08-14 09:38:29.788]","caller":"scheduler/scheduler.go:224","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-14 09:38:29.788]","caller":"scheduler/scheduler.go:233","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-14 09:38:29.789]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":9}
{"level":"dev.info","ts":"[2025-08-14 09:38:29.789]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-disbursement-compensation","description":"自动放款补偿任务 - 处理创建订单后自动放款异常或失败的补偿","schedule":"0 * * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:38:29.789]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-disbursement-compensation","task_type":"orders.AutoDisbursementCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 09:38:29.789]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"disbursement-status-sync-compensation","description":"放款状态同步补偿任务 - 处理订单状态为待放款但已有已提交放款流水的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:38:29.789]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"disbursement-status-sync-compensation","task_type":"orders.DisbursementStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 09:38:29.789]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"refund-status-sync-compensation","description":"退款状态同步补偿任务 - 处理已提交退款流水的状态同步","schedule":"0 */1 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:38:29.789]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"refund-status-sync-compensation","task_type":"orders.RefundStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 09:38:29.789]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-withhold-task","description":"系统自动代扣任务 - 自动处理待代扣账单","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton","timeout":1800,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:38:29.789]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-withhold-task","task_type":"repayment.AutoWithholdTask"}
{"level":"dev.info","ts":"[2025-08-14 09:38:29.789]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"repayment-status-sync-compensation","description":"还款状态同步补偿任务 - 处理还款账单状态与支付流水状态不一致的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:38:29.789]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"repayment-status-sync-compensation","task_type":"repayment.RepaymentStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 09:38:29.789]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-ahead","description":"账单到期提前通知任务 - 处理账单到期前一天，短信通知客户","schedule":"0 0 9 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:38:29.789]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-ahead","task_type":"notice.BillNoticeAheadTask"}
{"level":"dev.info","ts":"[2025-08-14 09:38:29.789]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-overdue","description":"账单逾期通知任务","schedule":"0 0 10 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:38:29.789]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-overdue","task_type":"notice.BillNoticeOverdueTask"}
{"level":"dev.info","ts":"[2025-08-14 09:38:29.789]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"available_quota_notice","description":"额度未使用通知任务","schedule":"0 0 11 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:38:29.789]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"available_quota_notice","task_type":"notice.AvailableQuotaNoticeTask"}
{"level":"dev.info","ts":"[2025-08-14 09:38:29.789]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"channel_statistics_task","description":"渠道统计任务：统计各渠道的新用户数、实名通过数、成交数","schedule":"0 0 */2 * * *","concurrency_mode":"singleton","timeout":600,"retry_count":2}
{"level":"dev.info","ts":"[2025-08-14 09:38:29.789]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"channel_statistics_task","task_type":"statistics.ChannelStatisticsTask"}
{"level":"dev.info","ts":"[2025-08-14 09:38:29.789]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":9,"success":9,"failure":0}
{"level":"dev.info","ts":"[2025-08-14 09:38:29.789]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-14 09:38:29.789]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-14 09:38:29.789]","caller":"scheduler/manager.go:124","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-14 09:38:29.789]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-08-14 09:38:29.789]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-withhold-task","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:38:29.789]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-ahead","schedule":"0 0 9 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:38:29.789]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-overdue","schedule":"0 0 10 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:38:29.789]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"refund-status-sync-compensation","schedule":"0 */1 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:38:29.789]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-disbursement-compensation","schedule":"0 * * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:38:29.789]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"disbursement-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:38:29.790]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"available_quota_notice","schedule":"0 0 11 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:38:29.790]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"repayment-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:38:29.790]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"channel_statistics_task","schedule":"0 0 */2 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:38:29.790]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":9,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-08-14 09:38:29.790]","caller":"scheduler/manager.go:139","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler","registered_tasks":9}
{"level":"dev.info","ts":"[2025-08-14 09:38:29.790]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-08-14 09:38:53.897]","caller":"engine/scheduler.go:99","msg":"正在停止调度引擎"}
{"level":"dev.info","ts":"[2025-08-14 09:38:53.897]","caller":"engine/scheduler.go:117","msg":"调度引擎已停止"}
{"level":"dev.info","ts":"[2025-08-14 09:38:53.897]","caller":"scheduler/manager.go:196","msg":"配置为等待正在运行的任务完成"}
{"level":"dev.info","ts":"[2025-08-14 09:38:53.897]","caller":"scheduler/manager.go:249","msg":"等待任务完成","配置超时时间：":60}
{"level":"dev.info","ts":"[2025-08-14 09:38:53.897]","caller":"scheduler/manager.go:257","msg":"所有任务已完成"}
{"level":"dev.info","ts":"[2025-08-14 09:38:53.897]","caller":"scheduler/manager.go:208","msg":"调度器管理器已停止","uptime":"24.1秒"}
{"level":"dev.info","ts":"[2025-08-14 09:38:53.897]","caller":"scheduler/scheduler.go:100","msg":"任务调度器已停止"}
{"level":"dev.info","ts":"[2025-08-14 09:39:01.065]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-14 09:39:01.066]","caller":"scheduler/manager.go:106","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler-dev","timezone":"Asia/Shanghai","max_concurrent_jobs":5}
{"level":"dev.info","ts":"[2025-08-14 09:39:01.066]","caller":"scheduler/scheduler.go:224","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-14 09:39:01.066]","caller":"scheduler/scheduler.go:233","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-14 09:39:01.067]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":9}
{"level":"dev.info","ts":"[2025-08-14 09:39:01.067]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-disbursement-compensation","description":"自动放款补偿任务 - 处理创建订单后自动放款异常或失败的补偿","schedule":"0 * * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:39:01.067]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-disbursement-compensation","task_type":"orders.AutoDisbursementCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 09:39:01.067]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"disbursement-status-sync-compensation","description":"放款状态同步补偿任务 - 处理订单状态为待放款但已有已提交放款流水的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:39:01.067]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"disbursement-status-sync-compensation","task_type":"orders.DisbursementStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 09:39:01.067]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"refund-status-sync-compensation","description":"退款状态同步补偿任务 - 处理已提交退款流水的状态同步","schedule":"0 */1 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:39:01.067]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"refund-status-sync-compensation","task_type":"orders.RefundStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 09:39:01.067]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-withhold-task","description":"系统自动代扣任务 - 自动处理待代扣账单","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton","timeout":1800,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:39:01.067]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-withhold-task","task_type":"repayment.AutoWithholdTask"}
{"level":"dev.info","ts":"[2025-08-14 09:39:01.067]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"repayment-status-sync-compensation","description":"还款状态同步补偿任务 - 处理还款账单状态与支付流水状态不一致的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:39:01.068]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"repayment-status-sync-compensation","task_type":"repayment.RepaymentStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 09:39:01.068]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-ahead","description":"账单到期提前通知任务 - 处理账单到期前一天，短信通知客户","schedule":"0 0 9 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:39:01.068]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-ahead","task_type":"notice.BillNoticeAheadTask"}
{"level":"dev.info","ts":"[2025-08-14 09:39:01.068]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-overdue","description":"账单逾期通知任务","schedule":"0 0 10 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:39:01.068]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-overdue","task_type":"notice.BillNoticeOverdueTask"}
{"level":"dev.info","ts":"[2025-08-14 09:39:01.068]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"available_quota_notice","description":"额度未使用通知任务","schedule":"0 0 11 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:39:01.068]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"available_quota_notice","task_type":"notice.AvailableQuotaNoticeTask"}
{"level":"dev.info","ts":"[2025-08-14 09:39:01.068]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"channel_statistics_task","description":"渠道统计任务：统计各渠道的新用户数、实名通过数、成交数","schedule":"0 0 */2 * * *","concurrency_mode":"singleton","timeout":600,"retry_count":2}
{"level":"dev.info","ts":"[2025-08-14 09:39:01.068]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"channel_statistics_task","task_type":"statistics.ChannelStatisticsTask"}
{"level":"dev.info","ts":"[2025-08-14 09:39:01.069]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":9,"success":9,"failure":0}
{"level":"dev.info","ts":"[2025-08-14 09:39:01.069]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-14 09:39:01.069]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-14 09:39:01.069]","caller":"scheduler/manager.go:124","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-14 09:39:01.069]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-08-14 09:39:01.069]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"channel_statistics_task","schedule":"0 0 */2 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:39:01.069]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-overdue","schedule":"0 0 10 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:39:01.069]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"refund-status-sync-compensation","schedule":"0 */1 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:39:01.069]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-ahead","schedule":"0 0 9 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:39:01.070]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"available_quota_notice","schedule":"0 0 11 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:39:01.070]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-disbursement-compensation","schedule":"0 * * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:39:01.070]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"disbursement-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:39:01.070]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-withhold-task","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:39:01.070]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"repayment-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:39:01.070]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":9,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-08-14 09:39:01.070]","caller":"scheduler/manager.go:139","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler-dev","registered_tasks":9}
{"level":"dev.info","ts":"[2025-08-14 09:39:01.070]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-08-14 09:39:30.303]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-14 09:39:30.303]","caller":"scheduler/manager.go:106","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler-dev","timezone":"Asia/Shanghai","max_concurrent_jobs":5}
{"level":"dev.info","ts":"[2025-08-14 09:39:30.304]","caller":"scheduler/scheduler.go:224","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-14 09:39:30.304]","caller":"scheduler/scheduler.go:233","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-14 09:39:30.305]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":9}
{"level":"dev.info","ts":"[2025-08-14 09:39:30.305]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-disbursement-compensation","description":"自动放款补偿任务 - 处理创建订单后自动放款异常或失败的补偿","schedule":"0 * * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:39:30.305]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-disbursement-compensation","task_type":"orders.AutoDisbursementCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 09:39:30.305]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"disbursement-status-sync-compensation","description":"放款状态同步补偿任务 - 处理订单状态为待放款但已有已提交放款流水的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:39:30.305]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"disbursement-status-sync-compensation","task_type":"orders.DisbursementStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 09:39:30.306]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"refund-status-sync-compensation","description":"退款状态同步补偿任务 - 处理已提交退款流水的状态同步","schedule":"0 */1 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:39:30.306]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"refund-status-sync-compensation","task_type":"orders.RefundStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 09:39:30.306]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-withhold-task","description":"系统自动代扣任务 - 自动处理待代扣账单","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton","timeout":1800,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:39:30.306]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-withhold-task","task_type":"repayment.AutoWithholdTask"}
{"level":"dev.info","ts":"[2025-08-14 09:39:30.306]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"repayment-status-sync-compensation","description":"还款状态同步补偿任务 - 处理还款账单状态与支付流水状态不一致的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:39:30.306]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"repayment-status-sync-compensation","task_type":"repayment.RepaymentStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 09:39:30.306]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-ahead","description":"账单到期提前通知任务 - 处理账单到期前一天，短信通知客户","schedule":"0 0 9 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:39:30.306]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-ahead","task_type":"notice.BillNoticeAheadTask"}
{"level":"dev.info","ts":"[2025-08-14 09:39:30.307]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-overdue","description":"账单逾期通知任务","schedule":"0 0 10 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:39:30.307]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-overdue","task_type":"notice.BillNoticeOverdueTask"}
{"level":"dev.info","ts":"[2025-08-14 09:39:30.307]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"available_quota_notice","description":"额度未使用通知任务","schedule":"0 0 11 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:39:30.307]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"available_quota_notice","task_type":"notice.AvailableQuotaNoticeTask"}
{"level":"dev.info","ts":"[2025-08-14 09:39:30.307]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"channel_statistics_task","description":"渠道统计任务：统计各渠道的新用户数、实名通过数、成交数","schedule":"0 0 */2 * * *","concurrency_mode":"singleton","timeout":600,"retry_count":2}
{"level":"dev.info","ts":"[2025-08-14 09:39:30.307]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"channel_statistics_task","task_type":"statistics.ChannelStatisticsTask"}
{"level":"dev.info","ts":"[2025-08-14 09:39:30.307]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":9,"success":9,"failure":0}
{"level":"dev.info","ts":"[2025-08-14 09:39:30.307]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-14 09:39:30.307]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-14 09:39:30.307]","caller":"scheduler/manager.go:124","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-14 09:39:30.307]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-08-14 09:39:30.307]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"refund-status-sync-compensation","schedule":"0 */1 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:39:30.307]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-ahead","schedule":"0 0 9 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:39:30.307]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"disbursement-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:39:30.308]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-withhold-task","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:39:30.308]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"repayment-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:39:30.308]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"available_quota_notice","schedule":"0 0 11 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:39:30.308]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"channel_statistics_task","schedule":"0 0 */2 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:39:30.309]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-disbursement-compensation","schedule":"0 * * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:39:30.309]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-overdue","schedule":"0 0 10 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:39:30.309]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":9,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-08-14 09:39:30.309]","caller":"scheduler/manager.go:139","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler-dev","registered_tasks":9}
{"level":"dev.info","ts":"[2025-08-14 09:39:30.309]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-08-14 09:40:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:40:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:40:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:40:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"disbursement-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:40:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"repayment-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:40:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"repayment-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:40:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:40:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"disbursement-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:40:00.259]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"refund-status-sync-compensation","job_id":1,"duration":0.2580494,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 09:40:00.259]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"refund-status-sync-compensation","job_id":2,"duration":0.2580494,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 09:40:00.259]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:40:00.259]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:40:00.778]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"disbursement-status-sync-compensation","job_id":2,"duration":0.777116,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 09:40:00.778]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"disbursement-status-sync-compensation","job_id":4,"duration":0.7754087,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 09:40:00.778]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"disbursement-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:40:00.778]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"disbursement-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:40:00.779]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"repayment-status-sync-compensation","job_id":3,"duration":0.777191,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 09:40:00.779]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"repayment-status-sync-compensation","job_id":3,"duration":0.7776981,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 09:40:00.779]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"repayment-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:40:00.779]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"repayment-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:40:00.781]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":1,"duration":0.7800727,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 09:40:00.781]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:40:00.781]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":4,"duration":0.7784228,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 09:40:00.781]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:40:24.520]","caller":"engine/scheduler.go:99","msg":"正在停止调度引擎"}
{"level":"dev.info","ts":"[2025-08-14 09:40:24.520]","caller":"engine/scheduler.go:117","msg":"调度引擎已停止"}
{"level":"dev.info","ts":"[2025-08-14 09:40:24.520]","caller":"scheduler/manager.go:191","msg":"配置为强制取消正在运行的任务"}
{"level":"dev.info","ts":"[2025-08-14 09:40:24.520]","caller":"scheduler/manager.go:219","msg":"没有正在运行的任务需要取消"}
{"level":"dev.info","ts":"[2025-08-14 09:40:24.520]","caller":"scheduler/manager.go:249","msg":"等待任务完成","配置超时时间：":30}
{"level":"dev.info","ts":"[2025-08-14 09:40:24.520]","caller":"scheduler/manager.go:257","msg":"所有任务已完成"}
{"level":"dev.info","ts":"[2025-08-14 09:40:24.521]","caller":"scheduler/manager.go:208","msg":"调度器管理器已停止","uptime":"54.2秒"}
{"level":"dev.info","ts":"[2025-08-14 09:40:24.521]","caller":"scheduler/scheduler.go:100","msg":"任务调度器已停止"}
{"level":"dev.info","ts":"[2025-08-14 09:40:42.686]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-14 09:40:42.688]","caller":"scheduler/manager.go:106","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler-dev","timezone":"Asia/Shanghai","max_concurrent_jobs":5}
{"level":"dev.info","ts":"[2025-08-14 09:40:42.688]","caller":"scheduler/scheduler.go:224","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-14 09:40:42.689]","caller":"scheduler/scheduler.go:233","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-14 09:40:42.691]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":9}
{"level":"dev.info","ts":"[2025-08-14 09:40:42.691]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-disbursement-compensation","description":"自动放款补偿任务 - 处理创建订单后自动放款异常或失败的补偿","schedule":"0 * * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:40:42.692]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-disbursement-compensation","task_type":"orders.AutoDisbursementCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 09:40:42.692]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"disbursement-status-sync-compensation","description":"放款状态同步补偿任务 - 处理订单状态为待放款但已有已提交放款流水的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:40:42.692]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"disbursement-status-sync-compensation","task_type":"orders.DisbursementStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 09:40:42.692]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"refund-status-sync-compensation","description":"退款状态同步补偿任务 - 处理已提交退款流水的状态同步","schedule":"0 */1 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:40:42.692]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"refund-status-sync-compensation","task_type":"orders.RefundStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 09:40:42.692]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-withhold-task","description":"系统自动代扣任务 - 自动处理待代扣账单","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton","timeout":1800,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:40:42.692]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-withhold-task","task_type":"repayment.AutoWithholdTask"}
{"level":"dev.info","ts":"[2025-08-14 09:40:42.692]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"repayment-status-sync-compensation","description":"还款状态同步补偿任务 - 处理还款账单状态与支付流水状态不一致的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:40:42.692]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"repayment-status-sync-compensation","task_type":"repayment.RepaymentStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 09:40:42.692]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-ahead","description":"账单到期提前通知任务 - 处理账单到期前一天，短信通知客户","schedule":"0 0 9 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:40:42.692]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-ahead","task_type":"notice.BillNoticeAheadTask"}
{"level":"dev.info","ts":"[2025-08-14 09:40:42.693]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-overdue","description":"账单逾期通知任务","schedule":"0 0 10 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:40:42.693]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-overdue","task_type":"notice.BillNoticeOverdueTask"}
{"level":"dev.info","ts":"[2025-08-14 09:40:42.693]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"available_quota_notice","description":"额度未使用通知任务","schedule":"0 0 11 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:40:42.694]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"available_quota_notice","task_type":"notice.AvailableQuotaNoticeTask"}
{"level":"dev.info","ts":"[2025-08-14 09:40:42.694]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"channel_statistics_task","description":"渠道统计任务：统计各渠道的新用户数、实名通过数、成交数","schedule":"0 0 */2 * * *","concurrency_mode":"singleton","timeout":600,"retry_count":2}
{"level":"dev.info","ts":"[2025-08-14 09:40:42.694]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"channel_statistics_task","task_type":"statistics.ChannelStatisticsTask"}
{"level":"dev.info","ts":"[2025-08-14 09:40:42.694]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":9,"success":9,"failure":0}
{"level":"dev.info","ts":"[2025-08-14 09:40:42.694]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-14 09:40:42.694]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-14 09:40:42.695]","caller":"scheduler/manager.go:124","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-14 09:40:42.695]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-08-14 09:40:42.695]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"repayment-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:40:42.695]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"channel_statistics_task","schedule":"0 0 */2 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:40:42.695]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-ahead","schedule":"0 0 9 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:40:42.696]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-overdue","schedule":"0 0 10 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:40:42.696]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"disbursement-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:40:42.696]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"refund-status-sync-compensation","schedule":"0 */1 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:40:42.696]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"available_quota_notice","schedule":"0 0 11 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:40:42.696]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-disbursement-compensation","schedule":"0 * * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:40:42.697]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-withhold-task","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:40:42.697]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":9,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-08-14 09:40:42.697]","caller":"scheduler/manager.go:139","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler-dev","registered_tasks":9}
{"level":"dev.info","ts":"[2025-08-14 09:40:42.697]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-08-14 09:41:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:41:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:41:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:41:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:41:00.024]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":2,"duration":0.0238934,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 09:41:00.024]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:41:00.058]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":6,"duration":0.0579482,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 09:41:00.058]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:41:00.072]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"refund-status-sync-compensation","job_id":1,"duration":0.0718154,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 09:41:00.073]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:41:00.073]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"refund-status-sync-compensation","job_id":5,"duration":0.0729312,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 09:41:00.073]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:41:38.428]","caller":"engine/scheduler.go:99","msg":"正在停止调度引擎"}
{"level":"dev.info","ts":"[2025-08-14 09:41:38.428]","caller":"engine/scheduler.go:117","msg":"调度引擎已停止"}
{"level":"dev.info","ts":"[2025-08-14 09:41:38.429]","caller":"scheduler/manager.go:191","msg":"配置为强制取消正在运行的任务"}
{"level":"dev.info","ts":"[2025-08-14 09:41:38.429]","caller":"scheduler/manager.go:219","msg":"没有正在运行的任务需要取消"}
{"level":"dev.info","ts":"[2025-08-14 09:41:38.429]","caller":"scheduler/manager.go:249","msg":"等待任务完成","配置超时时间：":30}
{"level":"dev.info","ts":"[2025-08-14 09:41:38.429]","caller":"scheduler/manager.go:257","msg":"所有任务已完成"}
{"level":"dev.info","ts":"[2025-08-14 09:41:38.429]","caller":"scheduler/manager.go:208","msg":"调度器管理器已停止","uptime":"55.7秒"}
{"level":"dev.info","ts":"[2025-08-14 09:41:38.429]","caller":"scheduler/scheduler.go:100","msg":"任务调度器已停止"}
{"level":"dev.info","ts":"[2025-08-14 09:42:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:42:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:42:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"repayment-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:42:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"disbursement-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:42:00.037]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"refund-status-sync-compensation","job_id":7,"duration":0.0363297,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 09:42:00.037]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:42:00.037]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":8,"duration":0.0357122,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 09:42:00.037]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:42:00.044]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"repayment-status-sync-compensation","job_id":9,"duration":0.0418361,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 09:42:00.044]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"repayment-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:42:00.044]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"disbursement-status-sync-compensation","job_id":10,"duration":0.0412637,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 09:42:00.044]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"disbursement-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:42:41.259]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-14 09:42:41.260]","caller":"scheduler/manager.go:106","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler-dev","timezone":"Asia/Shanghai","max_concurrent_jobs":5}
{"level":"dev.info","ts":"[2025-08-14 09:42:41.260]","caller":"scheduler/scheduler.go:224","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-14 09:42:41.260]","caller":"scheduler/scheduler.go:233","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-14 09:42:41.264]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":9}
{"level":"dev.info","ts":"[2025-08-14 09:42:41.265]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-disbursement-compensation","description":"自动放款补偿任务 - 处理创建订单后自动放款异常或失败的补偿","schedule":"0 * * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:42:41.265]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-disbursement-compensation","task_type":"orders.AutoDisbursementCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 09:42:41.265]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"disbursement-status-sync-compensation","description":"放款状态同步补偿任务 - 处理订单状态为待放款但已有已提交放款流水的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:42:41.266]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"disbursement-status-sync-compensation","task_type":"orders.DisbursementStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 09:42:41.266]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"refund-status-sync-compensation","description":"退款状态同步补偿任务 - 处理已提交退款流水的状态同步","schedule":"0 */1 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:42:41.266]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"refund-status-sync-compensation","task_type":"orders.RefundStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 09:42:41.266]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-withhold-task","description":"系统自动代扣任务 - 自动处理待代扣账单","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton","timeout":1800,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:42:41.266]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-withhold-task","task_type":"repayment.AutoWithholdTask"}
{"level":"dev.info","ts":"[2025-08-14 09:42:41.266]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"repayment-status-sync-compensation","description":"还款状态同步补偿任务 - 处理还款账单状态与支付流水状态不一致的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:42:41.266]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"repayment-status-sync-compensation","task_type":"repayment.RepaymentStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 09:42:41.266]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-ahead","description":"账单到期提前通知任务 - 处理账单到期前一天，短信通知客户","schedule":"0 0 9 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:42:41.266]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-ahead","task_type":"notice.BillNoticeAheadTask"}
{"level":"dev.info","ts":"[2025-08-14 09:42:41.266]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-overdue","description":"账单逾期通知任务","schedule":"0 0 10 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:42:41.266]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-overdue","task_type":"notice.BillNoticeOverdueTask"}
{"level":"dev.info","ts":"[2025-08-14 09:42:41.267]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"available_quota_notice","description":"额度未使用通知任务","schedule":"0 0 11 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:42:41.267]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"available_quota_notice","task_type":"notice.AvailableQuotaNoticeTask"}
{"level":"dev.info","ts":"[2025-08-14 09:42:41.267]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"channel_statistics_task","description":"渠道统计任务：统计各渠道的新用户数、实名通过数、成交数","schedule":"0 0 */2 * * *","concurrency_mode":"singleton","timeout":600,"retry_count":2}
{"level":"dev.info","ts":"[2025-08-14 09:42:41.267]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"channel_statistics_task","task_type":"statistics.ChannelStatisticsTask"}
{"level":"dev.info","ts":"[2025-08-14 09:42:41.267]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":9,"success":9,"failure":0}
{"level":"dev.info","ts":"[2025-08-14 09:42:41.267]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-14 09:42:41.267]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-14 09:42:41.267]","caller":"scheduler/manager.go:124","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-14 09:42:41.267]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-08-14 09:42:41.267]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-disbursement-compensation","schedule":"0 * * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:42:41.268]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"refund-status-sync-compensation","schedule":"0 */1 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:42:41.268]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-withhold-task","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:42:41.268]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"channel_statistics_task","schedule":"0 0 */2 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:42:41.268]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-ahead","schedule":"0 0 9 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:42:41.268]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"disbursement-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:42:41.268]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-overdue","schedule":"0 0 10 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:42:41.268]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"available_quota_notice","schedule":"0 0 11 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:42:41.269]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"repayment-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:42:41.269]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":9,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-08-14 09:42:41.269]","caller":"scheduler/manager.go:139","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler-dev","registered_tasks":9}
{"level":"dev.info","ts":"[2025-08-14 09:42:41.269]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-08-14 09:43:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:43:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:43:00.027]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"refund-status-sync-compensation","job_id":1,"duration":0.0272155,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 09:43:00.027]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:43:00.049]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":2,"duration":0.0485711,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 09:43:00.049]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:44:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"repayment-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:44:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"disbursement-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:44:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:44:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:44:00.116]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"disbursement-status-sync-compensation","job_id":4,"duration":0.1166747,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 09:44:00.116]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"disbursement-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:44:00.120]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"refund-status-sync-compensation","job_id":6,"duration":0.1196154,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 09:44:00.120]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:44:00.120]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"repayment-status-sync-compensation","job_id":3,"duration":0.1206256,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 09:44:00.120]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":5,"duration":0.1201211,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 09:44:00.120]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"repayment-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:44:00.120]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:44:15.165]","caller":"engine/scheduler.go:99","msg":"正在停止调度引擎"}
{"level":"dev.info","ts":"[2025-08-14 09:44:15.165]","caller":"engine/scheduler.go:117","msg":"调度引擎已停止"}
{"level":"dev.info","ts":"[2025-08-14 09:44:15.165]","caller":"scheduler/manager.go:191","msg":"配置为强制取消正在运行的任务"}
{"level":"dev.info","ts":"[2025-08-14 09:44:15.165]","caller":"scheduler/manager.go:219","msg":"没有正在运行的任务需要取消"}
{"level":"dev.info","ts":"[2025-08-14 09:44:15.165]","caller":"scheduler/manager.go:249","msg":"等待任务完成","配置超时时间：":30}
{"level":"dev.info","ts":"[2025-08-14 09:44:15.165]","caller":"scheduler/manager.go:257","msg":"所有任务已完成"}
{"level":"dev.info","ts":"[2025-08-14 09:44:15.165]","caller":"scheduler/manager.go:208","msg":"调度器管理器已停止","uptime":"1分钟 33秒"}
{"level":"dev.info","ts":"[2025-08-14 09:44:15.165]","caller":"scheduler/scheduler.go:100","msg":"任务调度器已停止"}
{"level":"dev.info","ts":"[2025-08-14 09:44:25.875]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-14 09:44:25.876]","caller":"scheduler/manager.go:106","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler","timezone":"Asia/Shanghai","max_concurrent_jobs":10}
{"level":"dev.info","ts":"[2025-08-14 09:44:25.876]","caller":"scheduler/scheduler.go:224","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-14 09:44:25.876]","caller":"scheduler/scheduler.go:233","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-14 09:44:25.877]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":9}
{"level":"dev.info","ts":"[2025-08-14 09:44:25.877]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-disbursement-compensation","description":"自动放款补偿任务 - 处理创建订单后自动放款异常或失败的补偿","schedule":"0 * * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:44:25.877]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-disbursement-compensation","task_type":"orders.AutoDisbursementCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 09:44:25.877]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"disbursement-status-sync-compensation","description":"放款状态同步补偿任务 - 处理订单状态为待放款但已有已提交放款流水的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:44:25.877]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"disbursement-status-sync-compensation","task_type":"orders.DisbursementStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 09:44:25.877]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"refund-status-sync-compensation","description":"退款状态同步补偿任务 - 处理已提交退款流水的状态同步","schedule":"0 */1 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:44:25.877]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"refund-status-sync-compensation","task_type":"orders.RefundStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 09:44:25.877]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-withhold-task","description":"系统自动代扣任务 - 自动处理待代扣账单","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton","timeout":1800,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:44:25.877]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-withhold-task","task_type":"repayment.AutoWithholdTask"}
{"level":"dev.info","ts":"[2025-08-14 09:44:25.877]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"repayment-status-sync-compensation","description":"还款状态同步补偿任务 - 处理还款账单状态与支付流水状态不一致的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:44:25.877]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"repayment-status-sync-compensation","task_type":"repayment.RepaymentStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 09:44:25.877]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-ahead","description":"账单到期提前通知任务 - 处理账单到期前一天，短信通知客户","schedule":"0 0 9 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:44:25.877]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-ahead","task_type":"notice.BillNoticeAheadTask"}
{"level":"dev.info","ts":"[2025-08-14 09:44:25.877]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-overdue","description":"账单逾期通知任务","schedule":"0 0 10 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:44:25.877]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-overdue","task_type":"notice.BillNoticeOverdueTask"}
{"level":"dev.info","ts":"[2025-08-14 09:44:25.877]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"available_quota_notice","description":"额度未使用通知任务","schedule":"0 0 11 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:44:25.877]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"available_quota_notice","task_type":"notice.AvailableQuotaNoticeTask"}
{"level":"dev.info","ts":"[2025-08-14 09:44:25.877]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"channel_statistics_task","description":"渠道统计任务：统计各渠道的新用户数、实名通过数、成交数","schedule":"0 0 */2 * * *","concurrency_mode":"singleton","timeout":600,"retry_count":2}
{"level":"dev.info","ts":"[2025-08-14 09:44:25.877]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"channel_statistics_task","task_type":"statistics.ChannelStatisticsTask"}
{"level":"dev.info","ts":"[2025-08-14 09:44:25.877]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":9,"success":9,"failure":0}
{"level":"dev.info","ts":"[2025-08-14 09:44:25.877]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-14 09:44:25.877]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-14 09:44:25.877]","caller":"scheduler/manager.go:124","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-14 09:44:25.877]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-08-14 09:44:25.877]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"available_quota_notice","schedule":"0 0 11 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:44:25.877]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"disbursement-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:44:25.877]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"repayment-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:44:25.877]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-overdue","schedule":"0 0 10 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:44:25.877]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"channel_statistics_task","schedule":"0 0 */2 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:44:25.877]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"refund-status-sync-compensation","schedule":"0 */1 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:44:25.878]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-ahead","schedule":"0 0 9 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:44:25.878]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-disbursement-compensation","schedule":"0 * * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:44:25.878]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-withhold-task","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:44:25.878]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":9,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-08-14 09:44:25.878]","caller":"scheduler/manager.go:139","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler","registered_tasks":9}
{"level":"dev.info","ts":"[2025-08-14 09:44:25.878]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-08-14 09:45:17.370]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-14 09:45:17.371]","caller":"scheduler/manager.go:106","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler","timezone":"Asia/Shanghai","max_concurrent_jobs":10}
{"level":"dev.info","ts":"[2025-08-14 09:45:17.371]","caller":"scheduler/scheduler.go:224","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-14 09:45:17.371]","caller":"scheduler/scheduler.go:233","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-14 09:45:17.372]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":9}
{"level":"dev.info","ts":"[2025-08-14 09:45:17.372]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-disbursement-compensation","description":"自动放款补偿任务 - 处理创建订单后自动放款异常或失败的补偿","schedule":"0 * * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:45:17.373]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-disbursement-compensation","task_type":"orders.AutoDisbursementCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 09:45:17.373]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"disbursement-status-sync-compensation","description":"放款状态同步补偿任务 - 处理订单状态为待放款但已有已提交放款流水的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:45:17.373]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"disbursement-status-sync-compensation","task_type":"orders.DisbursementStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 09:45:17.373]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"refund-status-sync-compensation","description":"退款状态同步补偿任务 - 处理已提交退款流水的状态同步","schedule":"0 */1 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:45:17.373]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"refund-status-sync-compensation","task_type":"orders.RefundStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 09:45:17.373]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-withhold-task","description":"系统自动代扣任务 - 自动处理待代扣账单","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton","timeout":1800,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:45:17.373]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-withhold-task","task_type":"repayment.AutoWithholdTask"}
{"level":"dev.info","ts":"[2025-08-14 09:45:17.373]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"repayment-status-sync-compensation","description":"还款状态同步补偿任务 - 处理还款账单状态与支付流水状态不一致的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:45:17.373]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"repayment-status-sync-compensation","task_type":"repayment.RepaymentStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 09:45:17.373]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-ahead","description":"账单到期提前通知任务 - 处理账单到期前一天，短信通知客户","schedule":"0 0 9 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:45:17.373]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-ahead","task_type":"notice.BillNoticeAheadTask"}
{"level":"dev.info","ts":"[2025-08-14 09:45:17.373]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-overdue","description":"账单逾期通知任务","schedule":"0 0 10 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:45:17.373]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-overdue","task_type":"notice.BillNoticeOverdueTask"}
{"level":"dev.info","ts":"[2025-08-14 09:45:17.373]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"available_quota_notice","description":"额度未使用通知任务","schedule":"0 0 11 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:45:17.373]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"available_quota_notice","task_type":"notice.AvailableQuotaNoticeTask"}
{"level":"dev.info","ts":"[2025-08-14 09:45:17.373]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"channel_statistics_task","description":"渠道统计任务：统计各渠道的新用户数、实名通过数、成交数","schedule":"0 0 */2 * * *","concurrency_mode":"singleton","timeout":600,"retry_count":2}
{"level":"dev.info","ts":"[2025-08-14 09:45:17.373]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"channel_statistics_task","task_type":"statistics.ChannelStatisticsTask"}
{"level":"dev.info","ts":"[2025-08-14 09:45:17.373]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":9,"success":9,"failure":0}
{"level":"dev.info","ts":"[2025-08-14 09:45:17.373]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-14 09:45:17.373]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-14 09:45:17.373]","caller":"scheduler/manager.go:124","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-14 09:45:17.373]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-08-14 09:45:17.373]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"repayment-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:45:17.373]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-ahead","schedule":"0 0 9 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:45:17.373]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"channel_statistics_task","schedule":"0 0 */2 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:45:17.373]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"refund-status-sync-compensation","schedule":"0 */1 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:45:17.373]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-overdue","schedule":"0 0 10 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:45:17.373]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"available_quota_notice","schedule":"0 0 11 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:45:17.373]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-disbursement-compensation","schedule":"0 * * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:45:17.373]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-withhold-task","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:45:17.373]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"disbursement-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:45:17.374]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":9,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-08-14 09:45:17.374]","caller":"scheduler/manager.go:139","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler","registered_tasks":9}
{"level":"dev.info","ts":"[2025-08-14 09:45:17.374]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-08-14 09:46:23.353]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-14 09:46:23.354]","caller":"scheduler/manager.go:106","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler","timezone":"Asia/Shanghai","max_concurrent_jobs":10}
{"level":"dev.info","ts":"[2025-08-14 09:46:23.354]","caller":"scheduler/scheduler.go:224","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-14 09:46:23.354]","caller":"scheduler/scheduler.go:233","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-14 09:46:23.355]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":9}
{"level":"dev.info","ts":"[2025-08-14 09:46:23.355]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-disbursement-compensation","description":"自动放款补偿任务 - 处理创建订单后自动放款异常或失败的补偿","schedule":"0 * * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:46:23.355]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-disbursement-compensation","task_type":"orders.AutoDisbursementCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 09:46:23.355]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"disbursement-status-sync-compensation","description":"放款状态同步补偿任务 - 处理订单状态为待放款但已有已提交放款流水的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:46:23.355]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"disbursement-status-sync-compensation","task_type":"orders.DisbursementStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 09:46:23.355]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"refund-status-sync-compensation","description":"退款状态同步补偿任务 - 处理已提交退款流水的状态同步","schedule":"0 */1 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:46:23.355]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"refund-status-sync-compensation","task_type":"orders.RefundStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 09:46:23.355]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-withhold-task","description":"系统自动代扣任务 - 自动处理待代扣账单","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton","timeout":1800,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:46:23.355]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-withhold-task","task_type":"repayment.AutoWithholdTask"}
{"level":"dev.info","ts":"[2025-08-14 09:46:23.355]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"repayment-status-sync-compensation","description":"还款状态同步补偿任务 - 处理还款账单状态与支付流水状态不一致的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:46:23.355]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"repayment-status-sync-compensation","task_type":"repayment.RepaymentStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 09:46:23.355]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-ahead","description":"账单到期提前通知任务 - 处理账单到期前一天，短信通知客户","schedule":"0 0 9 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:46:23.355]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-ahead","task_type":"notice.BillNoticeAheadTask"}
{"level":"dev.info","ts":"[2025-08-14 09:46:23.355]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-overdue","description":"账单逾期通知任务","schedule":"0 0 10 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:46:23.355]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-overdue","task_type":"notice.BillNoticeOverdueTask"}
{"level":"dev.info","ts":"[2025-08-14 09:46:23.355]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"available_quota_notice","description":"额度未使用通知任务","schedule":"0 0 11 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:46:23.355]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"available_quota_notice","task_type":"notice.AvailableQuotaNoticeTask"}
{"level":"dev.info","ts":"[2025-08-14 09:46:23.355]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"channel_statistics_task","description":"渠道统计任务：统计各渠道的新用户数、实名通过数、成交数","schedule":"0 0 */2 * * *","concurrency_mode":"singleton","timeout":600,"retry_count":2}
{"level":"dev.info","ts":"[2025-08-14 09:46:23.355]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"channel_statistics_task","task_type":"statistics.ChannelStatisticsTask"}
{"level":"dev.info","ts":"[2025-08-14 09:46:23.355]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":9,"success":9,"failure":0}
{"level":"dev.info","ts":"[2025-08-14 09:46:23.355]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-14 09:46:23.355]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-14 09:46:23.355]","caller":"scheduler/manager.go:124","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-14 09:46:23.355]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-08-14 09:46:23.355]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"refund-status-sync-compensation","schedule":"0 */1 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:46:23.355]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-withhold-task","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:46:23.355]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"available_quota_notice","schedule":"0 0 11 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:46:23.355]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-disbursement-compensation","schedule":"0 * * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:46:23.355]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-ahead","schedule":"0 0 9 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:46:23.355]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-overdue","schedule":"0 0 10 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:46:23.356]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"repayment-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:46:23.356]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"channel_statistics_task","schedule":"0 0 */2 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:46:23.356]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"disbursement-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:46:23.356]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":9,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-08-14 09:46:23.356]","caller":"scheduler/manager.go:139","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler","registered_tasks":9}
{"level":"dev.info","ts":"[2025-08-14 09:46:23.356]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-08-14 09:47:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:47:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:47:00.016]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"refund-status-sync-compensation","job_id":2,"duration":0.0160667,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 09:47:00.016]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:47:00.016]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":1,"duration":0.0165708,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 09:47:00.016]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:48:05.064]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:48:05.065]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"disbursement-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:48:05.065]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"repayment-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:48:05.065]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:48:05.080]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"refund-status-sync-compensation","job_id":3,"duration":0.0155957,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 09:48:05.080]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:48:05.108]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":6,"duration":0.0429105,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 09:48:05.108]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:48:05.108]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"repayment-status-sync-compensation","job_id":5,"duration":0.0434487,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 09:48:05.108]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"repayment-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:48:05.108]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"disbursement-status-sync-compensation","job_id":4,"duration":0.0434487,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 09:48:05.108]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"disbursement-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:49:00.002]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:49:00.002]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:49:00.014]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":8,"duration":0.0116054,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 09:49:00.014]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:49:00.014]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"refund-status-sync-compensation","job_id":7,"duration":0.0125942,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 09:49:00.014]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:49:45.923]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-14 09:49:45.924]","caller":"scheduler/manager.go:106","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler","timezone":"Asia/Shanghai","max_concurrent_jobs":10}
{"level":"dev.info","ts":"[2025-08-14 09:49:45.924]","caller":"scheduler/scheduler.go:224","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-14 09:49:45.924]","caller":"scheduler/scheduler.go:233","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-14 09:49:45.925]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":9}
{"level":"dev.info","ts":"[2025-08-14 09:49:45.925]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-disbursement-compensation","description":"自动放款补偿任务 - 处理创建订单后自动放款异常或失败的补偿","schedule":"0 * * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:49:45.925]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-disbursement-compensation","task_type":"orders.AutoDisbursementCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 09:49:45.925]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"disbursement-status-sync-compensation","description":"放款状态同步补偿任务 - 处理订单状态为待放款但已有已提交放款流水的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:49:45.925]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"disbursement-status-sync-compensation","task_type":"orders.DisbursementStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 09:49:45.925]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"refund-status-sync-compensation","description":"退款状态同步补偿任务 - 处理已提交退款流水的状态同步","schedule":"0 */1 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:49:45.925]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"refund-status-sync-compensation","task_type":"orders.RefundStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 09:49:45.925]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-withhold-task","description":"系统自动代扣任务 - 自动处理待代扣账单","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton","timeout":1800,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:49:45.925]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-withhold-task","task_type":"repayment.AutoWithholdTask"}
{"level":"dev.info","ts":"[2025-08-14 09:49:45.925]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"repayment-status-sync-compensation","description":"还款状态同步补偿任务 - 处理还款账单状态与支付流水状态不一致的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:49:45.925]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"repayment-status-sync-compensation","task_type":"repayment.RepaymentStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 09:49:45.925]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-ahead","description":"账单到期提前通知任务 - 处理账单到期前一天，短信通知客户","schedule":"0 0 9 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:49:45.925]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-ahead","task_type":"notice.BillNoticeAheadTask"}
{"level":"dev.info","ts":"[2025-08-14 09:49:45.925]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-overdue","description":"账单逾期通知任务","schedule":"0 0 10 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:49:45.925]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-overdue","task_type":"notice.BillNoticeOverdueTask"}
{"level":"dev.info","ts":"[2025-08-14 09:49:45.925]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"available_quota_notice","description":"额度未使用通知任务","schedule":"0 0 11 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:49:45.925]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"available_quota_notice","task_type":"notice.AvailableQuotaNoticeTask"}
{"level":"dev.info","ts":"[2025-08-14 09:49:45.925]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"channel_statistics_task","description":"渠道统计任务：统计各渠道的新用户数、实名通过数、成交数","schedule":"0 0 */2 * * *","concurrency_mode":"singleton","timeout":600,"retry_count":2}
{"level":"dev.info","ts":"[2025-08-14 09:49:45.925]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"channel_statistics_task","task_type":"statistics.ChannelStatisticsTask"}
{"level":"dev.info","ts":"[2025-08-14 09:49:45.925]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":9,"success":9,"failure":0}
{"level":"dev.info","ts":"[2025-08-14 09:49:45.925]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-14 09:49:45.925]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-14 09:49:45.925]","caller":"scheduler/manager.go:124","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-14 09:49:45.925]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-08-14 09:49:45.925]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-withhold-task","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:49:45.925]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"repayment-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:49:45.925]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-ahead","schedule":"0 0 9 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:49:45.926]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"channel_statistics_task","schedule":"0 0 */2 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:49:45.926]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"disbursement-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:49:45.926]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-overdue","schedule":"0 0 10 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:49:45.926]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"available_quota_notice","schedule":"0 0 11 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:49:45.926]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-disbursement-compensation","schedule":"0 * * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:49:45.926]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"refund-status-sync-compensation","schedule":"0 */1 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:49:45.926]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":9,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-08-14 09:49:45.926]","caller":"scheduler/manager.go:139","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler","registered_tasks":9}
{"level":"dev.info","ts":"[2025-08-14 09:49:45.926]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-08-14 09:50:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:50:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"repayment-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:50:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:50:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"disbursement-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:50:00.029]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"refund-status-sync-compensation","job_id":3,"duration":0.0290225,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 09:50:00.029]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:50:00.043]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":1,"duration":0.0432369,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 09:50:00.043]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:50:00.043]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"repayment-status-sync-compensation","job_id":2,"duration":0.0432369,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 09:50:00.043]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"repayment-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:50:00.048]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"disbursement-status-sync-compensation","job_id":4,"duration":0.0464616,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 09:50:00.048]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"disbursement-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:51:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:51:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:51:00.041]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"refund-status-sync-compensation","job_id":6,"duration":0.0411344,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 09:51:00.041]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:51:00.042]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":5,"duration":0.0418292,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 09:51:00.042]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:52:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"repayment-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:52:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:52:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:52:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"disbursement-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:52:00.081]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"disbursement-status-sync-compensation","job_id":10,"duration":0.0809701,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 09:52:00.081]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"disbursement-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:52:00.098]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":9,"duration":0.097785,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 09:52:00.098]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:52:00.106]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"refund-status-sync-compensation","job_id":8,"duration":0.10601,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 09:52:00.106]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"repayment-status-sync-compensation","job_id":7,"duration":0.10601,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 09:52:00.106]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:52:00.106]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"repayment-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:53:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:53:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:53:00.052]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":12,"duration":0.0521,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 09:53:00.052]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:53:00.054]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"refund-status-sync-compensation","job_id":11,"duration":0.053853,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 09:53:00.054]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:54:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"repayment-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:54:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:54:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:54:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"disbursement-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:54:00.054]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"refund-status-sync-compensation","job_id":14,"duration":0.0542266,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 09:54:00.054]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:54:00.064]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"disbursement-status-sync-compensation","job_id":16,"duration":0.0630952,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 09:54:00.064]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"disbursement-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:54:00.066]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"repayment-status-sync-compensation","job_id":13,"duration":0.065303,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 09:54:00.066]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"repayment-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:54:00.066]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":15,"duration":0.065303,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 09:54:00.066]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:55:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:55:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:55:00.077]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"refund-status-sync-compensation","job_id":18,"duration":0.0762922,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 09:55:00.077]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:55:00.077]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":17,"duration":0.0767995,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 09:55:00.077]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:56:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:56:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"repayment-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:56:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:56:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"disbursement-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:56:00.069]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":19,"duration":0.0689594,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 09:56:00.069]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:56:00.074]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"refund-status-sync-compensation","job_id":21,"duration":0.0738538,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 09:56:00.074]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:56:00.081]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"repayment-status-sync-compensation","job_id":20,"duration":0.0812061,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 09:56:00.081]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"repayment-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:56:00.081]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"disbursement-status-sync-compensation","job_id":22,"duration":0.0807004,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 09:56:00.081]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"disbursement-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:57:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:57:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:57:00.082]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"refund-status-sync-compensation","job_id":24,"duration":0.0818799,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 09:57:00.082]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:57:00.082]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":23,"duration":0.0818799,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 09:57:00.082]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:58:22.261]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-14 09:58:22.262]","caller":"scheduler/manager.go:106","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler","timezone":"Asia/Shanghai","max_concurrent_jobs":10}
{"level":"dev.info","ts":"[2025-08-14 09:58:22.263]","caller":"scheduler/scheduler.go:224","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-14 09:58:22.263]","caller":"scheduler/scheduler.go:233","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-14 09:58:22.265]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":9}
{"level":"dev.info","ts":"[2025-08-14 09:58:22.265]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-disbursement-compensation","description":"自动放款补偿任务 - 处理创建订单后自动放款异常或失败的补偿","schedule":"0 * * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:58:22.265]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-disbursement-compensation","task_type":"orders.AutoDisbursementCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 09:58:22.265]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"disbursement-status-sync-compensation","description":"放款状态同步补偿任务 - 处理订单状态为待放款但已有已提交放款流水的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:58:22.265]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"disbursement-status-sync-compensation","task_type":"orders.DisbursementStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 09:58:22.265]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"refund-status-sync-compensation","description":"退款状态同步补偿任务 - 处理已提交退款流水的状态同步","schedule":"0 */1 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:58:22.265]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"refund-status-sync-compensation","task_type":"orders.RefundStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 09:58:22.265]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-withhold-task","description":"系统自动代扣任务 - 自动处理待代扣账单","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton","timeout":1800,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:58:22.265]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-withhold-task","task_type":"repayment.AutoWithholdTask"}
{"level":"dev.info","ts":"[2025-08-14 09:58:22.265]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"repayment-status-sync-compensation","description":"还款状态同步补偿任务 - 处理还款账单状态与支付流水状态不一致的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:58:22.265]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"repayment-status-sync-compensation","task_type":"repayment.RepaymentStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 09:58:22.265]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-ahead","description":"账单到期提前通知任务 - 处理账单到期前一天，短信通知客户","schedule":"0 0 9 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:58:22.265]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-ahead","task_type":"notice.BillNoticeAheadTask"}
{"level":"dev.info","ts":"[2025-08-14 09:58:22.265]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-overdue","description":"账单逾期通知任务","schedule":"0 0 10 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:58:22.265]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-overdue","task_type":"notice.BillNoticeOverdueTask"}
{"level":"dev.info","ts":"[2025-08-14 09:58:22.265]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"available_quota_notice","description":"额度未使用通知任务","schedule":"0 0 11 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:58:22.265]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"available_quota_notice","task_type":"notice.AvailableQuotaNoticeTask"}
{"level":"dev.info","ts":"[2025-08-14 09:58:22.265]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"channel_statistics_task","description":"渠道统计任务：统计各渠道的新用户数、实名通过数、成交数","schedule":"0 0 */2 * * *","concurrency_mode":"singleton","timeout":600,"retry_count":2}
{"level":"dev.info","ts":"[2025-08-14 09:58:22.265]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"channel_statistics_task","task_type":"statistics.ChannelStatisticsTask"}
{"level":"dev.info","ts":"[2025-08-14 09:58:22.265]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":9,"success":9,"failure":0}
{"level":"dev.info","ts":"[2025-08-14 09:58:22.265]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-14 09:58:22.265]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-14 09:58:22.265]","caller":"scheduler/manager.go:124","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-14 09:58:22.265]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-08-14 09:58:22.265]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-disbursement-compensation","schedule":"0 * * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:58:22.266]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"disbursement-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:58:22.266]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-ahead","schedule":"0 0 9 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:58:22.266]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"channel_statistics_task","schedule":"0 0 */2 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:58:22.266]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-overdue","schedule":"0 0 10 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:58:22.266]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-withhold-task","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:58:22.266]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"repayment-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:58:22.266]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"available_quota_notice","schedule":"0 0 11 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:58:22.266]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"refund-status-sync-compensation","schedule":"0 */1 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:58:22.266]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":9,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-08-14 09:58:22.266]","caller":"scheduler/manager.go:139","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler","registered_tasks":9}
{"level":"dev.info","ts":"[2025-08-14 09:58:22.266]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-08-14 09:59:04.092]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-14 09:59:04.092]","caller":"scheduler/manager.go:106","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler","timezone":"Asia/Shanghai","max_concurrent_jobs":10}
{"level":"dev.info","ts":"[2025-08-14 09:59:04.092]","caller":"scheduler/scheduler.go:224","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-14 09:59:04.092]","caller":"scheduler/scheduler.go:233","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-14 09:59:04.094]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":9}
{"level":"dev.info","ts":"[2025-08-14 09:59:04.094]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-disbursement-compensation","description":"自动放款补偿任务 - 处理创建订单后自动放款异常或失败的补偿","schedule":"0 * * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:59:04.094]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-disbursement-compensation","task_type":"orders.AutoDisbursementCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 09:59:04.094]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"disbursement-status-sync-compensation","description":"放款状态同步补偿任务 - 处理订单状态为待放款但已有已提交放款流水的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:59:04.094]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"disbursement-status-sync-compensation","task_type":"orders.DisbursementStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 09:59:04.094]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"refund-status-sync-compensation","description":"退款状态同步补偿任务 - 处理已提交退款流水的状态同步","schedule":"0 */1 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:59:04.094]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"refund-status-sync-compensation","task_type":"orders.RefundStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 09:59:04.094]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-withhold-task","description":"系统自动代扣任务 - 自动处理待代扣账单","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton","timeout":1800,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:59:04.094]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-withhold-task","task_type":"repayment.AutoWithholdTask"}
{"level":"dev.info","ts":"[2025-08-14 09:59:04.094]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"repayment-status-sync-compensation","description":"还款状态同步补偿任务 - 处理还款账单状态与支付流水状态不一致的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:59:04.094]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"repayment-status-sync-compensation","task_type":"repayment.RepaymentStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 09:59:04.094]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-ahead","description":"账单到期提前通知任务 - 处理账单到期前一天，短信通知客户","schedule":"0 0 9 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:59:04.094]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-ahead","task_type":"notice.BillNoticeAheadTask"}
{"level":"dev.info","ts":"[2025-08-14 09:59:04.094]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-overdue","description":"账单逾期通知任务","schedule":"0 0 10 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:59:04.094]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-overdue","task_type":"notice.BillNoticeOverdueTask"}
{"level":"dev.info","ts":"[2025-08-14 09:59:04.094]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"available_quota_notice","description":"额度未使用通知任务","schedule":"0 0 11 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:59:04.094]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"available_quota_notice","task_type":"notice.AvailableQuotaNoticeTask"}
{"level":"dev.info","ts":"[2025-08-14 09:59:04.094]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"channel_statistics_task","description":"渠道统计任务：统计各渠道的新用户数、实名通过数、成交数","schedule":"0 0 */2 * * *","concurrency_mode":"singleton","timeout":600,"retry_count":2}
{"level":"dev.info","ts":"[2025-08-14 09:59:04.094]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"channel_statistics_task","task_type":"statistics.ChannelStatisticsTask"}
{"level":"dev.info","ts":"[2025-08-14 09:59:04.094]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":9,"success":9,"failure":0}
{"level":"dev.info","ts":"[2025-08-14 09:59:04.094]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-14 09:59:04.094]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-14 09:59:04.094]","caller":"scheduler/manager.go:124","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-14 09:59:04.094]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-08-14 09:59:04.094]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-ahead","schedule":"0 0 9 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:59:04.095]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-disbursement-compensation","schedule":"0 * * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:59:04.095]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-withhold-task","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:59:04.095]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"repayment-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:59:04.095]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-overdue","schedule":"0 0 10 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:59:04.095]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"available_quota_notice","schedule":"0 0 11 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:59:04.095]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"channel_statistics_task","schedule":"0 0 */2 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:59:04.095]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"disbursement-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:59:04.095]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"refund-status-sync-compensation","schedule":"0 */1 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:59:04.095]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":9,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-08-14 09:59:04.095]","caller":"scheduler/manager.go:139","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler","registered_tasks":9}
{"level":"dev.info","ts":"[2025-08-14 09:59:04.095]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-08-14 09:59:55.116]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-14 09:59:55.118]","caller":"scheduler/manager.go:106","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler","timezone":"Asia/Shanghai","max_concurrent_jobs":10}
{"level":"dev.info","ts":"[2025-08-14 09:59:55.118]","caller":"scheduler/scheduler.go:224","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-14 09:59:55.118]","caller":"scheduler/scheduler.go:233","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-14 09:59:55.119]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":9}
{"level":"dev.info","ts":"[2025-08-14 09:59:55.119]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-disbursement-compensation","description":"自动放款补偿任务 - 处理创建订单后自动放款异常或失败的补偿","schedule":"0 * * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:59:55.119]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-disbursement-compensation","task_type":"orders.AutoDisbursementCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 09:59:55.119]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"disbursement-status-sync-compensation","description":"放款状态同步补偿任务 - 处理订单状态为待放款但已有已提交放款流水的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:59:55.119]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"disbursement-status-sync-compensation","task_type":"orders.DisbursementStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 09:59:55.119]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"refund-status-sync-compensation","description":"退款状态同步补偿任务 - 处理已提交退款流水的状态同步","schedule":"0 */1 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:59:55.119]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"refund-status-sync-compensation","task_type":"orders.RefundStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 09:59:55.119]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-withhold-task","description":"系统自动代扣任务 - 自动处理待代扣账单","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton","timeout":1800,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:59:55.119]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-withhold-task","task_type":"repayment.AutoWithholdTask"}
{"level":"dev.info","ts":"[2025-08-14 09:59:55.119]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"repayment-status-sync-compensation","description":"还款状态同步补偿任务 - 处理还款账单状态与支付流水状态不一致的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:59:55.120]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"repayment-status-sync-compensation","task_type":"repayment.RepaymentStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 09:59:55.120]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-ahead","description":"账单到期提前通知任务 - 处理账单到期前一天，短信通知客户","schedule":"0 0 9 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:59:55.120]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-ahead","task_type":"notice.BillNoticeAheadTask"}
{"level":"dev.info","ts":"[2025-08-14 09:59:55.120]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-overdue","description":"账单逾期通知任务","schedule":"0 0 10 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:59:55.120]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-overdue","task_type":"notice.BillNoticeOverdueTask"}
{"level":"dev.info","ts":"[2025-08-14 09:59:55.120]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"available_quota_notice","description":"额度未使用通知任务","schedule":"0 0 11 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:59:55.120]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"available_quota_notice","task_type":"notice.AvailableQuotaNoticeTask"}
{"level":"dev.info","ts":"[2025-08-14 09:59:55.120]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"channel_statistics_task","description":"渠道统计任务：统计各渠道的新用户数、实名通过数、成交数","schedule":"0 0 */2 * * *","concurrency_mode":"singleton","timeout":600,"retry_count":2}
{"level":"dev.info","ts":"[2025-08-14 09:59:55.120]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"channel_statistics_task","task_type":"statistics.ChannelStatisticsTask"}
{"level":"dev.info","ts":"[2025-08-14 09:59:55.120]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":9,"success":9,"failure":0}
{"level":"dev.info","ts":"[2025-08-14 09:59:55.120]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-14 09:59:55.120]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-14 09:59:55.120]","caller":"scheduler/manager.go:124","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-14 09:59:55.120]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-08-14 09:59:55.121]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-overdue","schedule":"0 0 10 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:59:55.121]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"repayment-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:59:55.121]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-ahead","schedule":"0 0 9 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:59:55.121]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"channel_statistics_task","schedule":"0 0 */2 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:59:55.121]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"available_quota_notice","schedule":"0 0 11 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:59:55.121]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-disbursement-compensation","schedule":"0 * * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:59:55.121]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"disbursement-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:59:55.122]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"refund-status-sync-compensation","schedule":"0 */1 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:59:55.122]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-withhold-task","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:59:55.122]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":9,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-08-14 09:59:55.122]","caller":"scheduler/manager.go:139","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler","registered_tasks":9}
{"level":"dev.info","ts":"[2025-08-14 09:59:55.122]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-08-14 10:00:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"channel_statistics_task"}
{"level":"dev.info","ts":"[2025-08-14 10:00:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 10:00:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"repayment-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 10:00:00.001]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"disbursement-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 10:00:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"bill-notice-overdue"}
{"level":"dev.info","ts":"[2025-08-14 10:00:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-14 10:00:00.017]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"refund-status-sync-compensation","job_id":2,"duration":0.0166756,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 10:00:00.017]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 10:00:00.018]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"repayment-status-sync-compensation","job_id":3,"duration":0.0171805,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 10:00:00.018]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"repayment-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 10:00:00.018]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"disbursement-status-sync-compensation","job_id":4,"duration":0.0160614,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 10:00:00.018]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"disbursement-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 10:00:00.046]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":6,"duration":0.0440972,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 10:00:00.046]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-14 10:00:00.047]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"bill-notice-overdue","job_id":5,"duration":0.0451209,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 10:00:00.047]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"bill-notice-overdue"}
{"level":"dev.info","ts":"[2025-08-14 10:00:00.133]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"channel_statistics_task","job_id":1,"duration":0.1322637,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 10:00:00.133]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"channel_statistics_task"}
{"level":"dev.info","ts":"[2025-08-14 10:10:44.586]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-14 10:10:44.587]","caller":"scheduler/manager.go:106","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler","timezone":"Asia/Shanghai","max_concurrent_jobs":10}
{"level":"dev.info","ts":"[2025-08-14 10:10:44.587]","caller":"scheduler/scheduler.go:224","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-14 10:10:44.587]","caller":"scheduler/scheduler.go:233","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-14 10:10:44.588]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":9}
{"level":"dev.info","ts":"[2025-08-14 10:10:44.588]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-disbursement-compensation","description":"自动放款补偿任务 - 处理创建订单后自动放款异常或失败的补偿","schedule":"0 * * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 10:10:44.588]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-disbursement-compensation","task_type":"orders.AutoDisbursementCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 10:10:44.588]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"disbursement-status-sync-compensation","description":"放款状态同步补偿任务 - 处理订单状态为待放款但已有已提交放款流水的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 10:10:44.588]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"disbursement-status-sync-compensation","task_type":"orders.DisbursementStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 10:10:44.588]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"refund-status-sync-compensation","description":"退款状态同步补偿任务 - 处理已提交退款流水的状态同步","schedule":"0 */1 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 10:10:44.588]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"refund-status-sync-compensation","task_type":"orders.RefundStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 10:10:44.588]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-withhold-task","description":"系统自动代扣任务 - 自动处理待代扣账单","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton","timeout":1800,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 10:10:44.588]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-withhold-task","task_type":"repayment.AutoWithholdTask"}
{"level":"dev.info","ts":"[2025-08-14 10:10:44.588]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"repayment-status-sync-compensation","description":"还款状态同步补偿任务 - 处理还款账单状态与支付流水状态不一致的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 10:10:44.588]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"repayment-status-sync-compensation","task_type":"repayment.RepaymentStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 10:10:44.588]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-ahead","description":"账单到期提前通知任务 - 处理账单到期前一天，短信通知客户","schedule":"0 0 9 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 10:10:44.588]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-ahead","task_type":"notice.BillNoticeAheadTask"}
{"level":"dev.info","ts":"[2025-08-14 10:10:44.588]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-overdue","description":"账单逾期通知任务","schedule":"0 0 10 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 10:10:44.588]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-overdue","task_type":"notice.BillNoticeOverdueTask"}
{"level":"dev.info","ts":"[2025-08-14 10:10:44.588]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"available_quota_notice","description":"额度未使用通知任务","schedule":"0 0 11 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 10:10:44.588]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"available_quota_notice","task_type":"notice.AvailableQuotaNoticeTask"}
{"level":"dev.info","ts":"[2025-08-14 10:10:44.588]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"channel_statistics_task","description":"渠道统计任务：统计各渠道的新用户数、实名通过数、成交数","schedule":"0 0 */2 * * *","concurrency_mode":"singleton","timeout":600,"retry_count":2}
{"level":"dev.info","ts":"[2025-08-14 10:10:44.588]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"channel_statistics_task","task_type":"statistics.ChannelStatisticsTask"}
{"level":"dev.info","ts":"[2025-08-14 10:10:44.588]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":9,"success":9,"failure":0}
{"level":"dev.info","ts":"[2025-08-14 10:10:44.588]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-14 10:10:44.588]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-14 10:10:44.588]","caller":"scheduler/manager.go:124","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-14 10:10:44.588]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-08-14 10:10:44.589]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-disbursement-compensation","schedule":"0 * * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 10:10:44.589]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-overdue","schedule":"0 0 10 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 10:10:44.589]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"available_quota_notice","schedule":"0 0 11 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 10:10:44.589]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"repayment-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 10:10:44.589]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-ahead","schedule":"0 0 9 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 10:10:44.589]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"channel_statistics_task","schedule":"0 0 */2 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 10:10:44.589]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"disbursement-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 10:10:44.589]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"refund-status-sync-compensation","schedule":"0 */1 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 10:10:44.589]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-withhold-task","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 10:10:44.589]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":9,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-08-14 10:10:44.589]","caller":"scheduler/manager.go:139","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler","registered_tasks":9}
{"level":"dev.info","ts":"[2025-08-14 10:10:44.589]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-08-14 10:11:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-14 10:11:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 10:11:00.025]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"refund-status-sync-compensation","job_id":2,"duration":0.0250069,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 10:11:00.025]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 10:11:00.025]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":1,"duration":0.0250069,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 10:11:00.025]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-14 10:12:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 10:12:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-14 10:12:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"repayment-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 10:12:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"disbursement-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 10:12:00.053]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"refund-status-sync-compensation","job_id":3,"duration":0.0534872,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 10:12:00.053]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 10:12:00.053]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"repayment-status-sync-compensation","job_id":5,"duration":0.052975,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 10:12:00.053]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"repayment-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 10:12:00.053]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"disbursement-status-sync-compensation","job_id":6,"duration":0.052975,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 10:12:00.053]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"disbursement-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 10:12:00.053]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":4,"duration":0.0534872,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 10:12:00.053]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-14 10:13:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-14 10:13:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 10:13:00.048]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":7,"duration":0.0481792,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 10:13:00.048]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-14 10:13:00.052]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"refund-status-sync-compensation","job_id":8,"duration":0.0520148,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 10:13:00.052]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 10:14:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"disbursement-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 10:14:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 10:14:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"repayment-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 10:14:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-14 10:14:00.149]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"refund-status-sync-compensation","job_id":10,"duration":0.1491443,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 10:14:00.149]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 10:14:00.150]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"repayment-status-sync-compensation","job_id":11,"duration":0.1501586,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 10:14:00.150]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"repayment-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 10:14:00.157]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":12,"duration":0.1561195,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 10:14:00.157]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-14 10:14:00.157]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"disbursement-status-sync-compensation","job_id":9,"duration":0.1567083,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 10:14:00.157]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"disbursement-status-sync-compensation"}
