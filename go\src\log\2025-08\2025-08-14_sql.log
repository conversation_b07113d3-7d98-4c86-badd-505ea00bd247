{"level":"dev.info","ts":"[2025-08-14 09:40:00.251]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"task_repayment-status-sync-compensation_20250814094000","sql":"\n\t\tSELECT \n\t\t\tid,\n\t\t\ttransaction_no,\n\t\t\torder_no,\n\t\t\tamount,\n\t\t\tcreated_at\n\t\tFROM business_payment_transactions \n\t\tWHERE type = ?\n\t\t  AND status = ?\n\t\t  AND deleted_at IS NULL\n\t\tORDER BY id ASC \n\t\t{"level":"dev.info","ts":"[2025-08-14 09:40:00.770]","caller":"gform/{"level":"dev.info","ts":"[2025-08-14 09:40:00.770]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"task_repayment-status-sync-compensation_20250814094000","sql":"\n\t\tSELECT DISTINCT\n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tbpt.transaction_no,\n\t\t\tbpt.channel_transaction_no,\n\t\t\tbpt.status as transaction_status\n\t\tFROM business_loan_orders blo\n\t\tINNER JOIN business_payment_transactions bpt ON blo.order_no = bpt.order_no\n\t\tWHERE blo.status = ?\n\t\t  AND bpt.type = ?\n\t\t  AND bpt.status = ?\n\t\t  AND bpt.channel_transaction_no != ''\n\t\t  AND bpt.channel_transaction_no IS NOT NULL\n\t\t  AND {"level":"dev.info","ts":"[2025-08-14 09:40:00.778]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT DISTINCT\n\t\t\tbrb.id as bill_id,\n\t\t\tbrb.order_id,\n\t\t\tbrb.user_id,\n\t\t\tbrb.period_number,\n\t\t\tbrb.status as bill_status,\n\t\t\tbrb.total_due_amount,\n\t\t\tbrb.paid_amount,\n\t\t\tbpt.transaction_no,\n\t\t\tbpt.type as transaction_type,\n\t\t\tbpt.status as transaction_status,\n\t\t\tbpt.amount,\n\t\t\tbpt.third_party_order_no,\n\t\t\tbpt.created_at as transaction_created_at\n\t\tFROM business_repayment_bills brb\n\t\tINNER JOIN business_payment_transactions bpt ON brb.id = bpt.bill_id\n\t\tWHERE brb.status IN (?, ?, ?, ?)\n\t\t  AND bpt.status = ?\n\t\t  AND bpt.type IN (?, ?, ?)\n\t\t  AND bpt.transaction_no != ''\n\t\t  AND bpt.transaction_no IS NOT NULL\n\t\t  AND bpt.deleted_at IS NULL\n\t\tORDER BY bpt.created_at ASC\n\t\tLIMIT 100\n\t, [0 3 7 9 1 REPAYMENT WITHHOLD MANUAL_WITHHOLD]","duration":"762.4947ms","duration_ms":762}
{"level":"dev.info","ts":"[2025-08-14 09:40:00.778]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tc.channel_name,\n\t\t\tc.auto_label\n\t\tFROM business_loan_orders blo\n\t\tLEFT JOIN channel c ON blo.channel_id = c.id\n\t\tWHERE blo.status = ?\n\t\t  AND blo.risk_control_results = 0\n\t\t  AND c.auto_label = 0\n\t\t  AND c.channel_status = 1\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0]","duration":"761.3095ms","duration_ms":761}
{"level":"dev.info","ts":"[2025-08-14 09:41:00.058]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tc.channel_name,\n\t\t\tc.auto_label\n\t\tFROM business_loan_orders blo\n\t\tLEFT JOIN channel c ON blo.channel_id = c.id\n\t\tWHERE blo.status = ?\n\t\t  AND blo.risk_control_results = 0\n\t\t  AND c.auto_label = 0\n\t\t  AND c.channel_status = 1\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0]","duration":"57.445ms","duration_ms":57}
{"level":"dev.info","ts":"[2025-08-14 09:41:00.072]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tid,\n\t\t\ttransaction_no,\n\t\t\torder_no,\n\t\t\tamount,\n\t\t\tcreated_at\n\t\tFROM business_payment_transactions \n\t\tWHERE type = ?\n\t\t  AND status = ?\n\t\t  AND deleted_at IS NULL\n\t\tORDER BY id ASC \n\t\tLIMIT 100\n\t, [REFUND 1]","duration":"63.8367ms","duration_ms":63}
{"level":"dev.info","ts":"[2025-08-14 09:42:00.036]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tc.channel_name,\n\t\t\tc.auto_label\n\t\tFROM business_loan_orders blo\n\t\tLEFT JOIN channel c ON blo.channel_id = c.id\n\t\tWHERE blo.status = ?\n\t\t  AND blo.risk_control_results = 0\n\t\t  AND c.auto_label = 0\n\t\t  AND c.channel_status = 1\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0]","duration":"33.4601ms","duration_ms":33}
{"level":"dev.info","ts":"[2025-08-14 09:42:00.036]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tid,\n\t\t\ttransaction_no,\n\t\t\torder_no,\n\t\t\tamount,\n\t\t\tcreated_at\n\t\tFROM business_payment_transactions \n\t\tWHERE type = ?\n\t\t  AND status = ?\n\t\t  AND deleted_at IS NULL\n\t\tORDER BY id ASC \n\t\tLIMIT 100\n\t, [REFUND 1]","duration":"34.6241ms","duration_ms":34}
{"level":"dev.info","ts":"[2025-08-14 09:42:00.043]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT DISTINCT\n\t\t\tbrb.id as bill_id,\n\t\t\tbrb.order_id,\n\t\t\tbrb.user_id,\n\t\t\tbrb.period_number,\n\t\t\tbrb.status as bill_status,\n\t\t\tbrb.total_due_amount,\n\t\t\tbrb.paid_amount,\n\t\t\tbpt.transaction_no,\n\t\t\tbpt.type as transaction_type,\n\t\t\tbpt.status as transaction_status,\n\t\t\tbpt.amount,\n\t\t\tbpt.third_party_order_no,\n\t\t\tbpt.created_at as transaction_created_at\n\t\tFROM business_repayment_bills brb\n\t\tINNER JOIN business_payment_transactions bpt ON brb.id = bpt.bill_id\n\t\tWHERE brb.status IN (?, ?, ?, ?)\n\t\t  AND bpt.status = ?\n\t\t  AND bpt.type IN (?, ?, ?)\n\t\t  AND bpt.transaction_no != ''\n\t\t  AND bpt.transaction_no IS NOT NULL\n\t\t  AND bpt.deleted_at IS NULL\n\t\tORDER BY bpt.created_at ASC\n\t\tLIMIT 100\n\t, [0 3 7 9 1 REPAYMENT WITHHOLD MANUAL_WITHHOLD]","duration":"40.0265ms","duration_ms":40}
{"level":"dev.info","ts":"[2025-08-14 09:42:00.043]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT DISTINCT\n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tbpt.transaction_no,\n\t\t\tbpt.channel_transaction_no,\n\t\t\tbpt.status as transaction_status\n\t\tFROM business_loan_orders blo\n\t\tINNER JOIN business_payment_transactions bpt ON blo.order_no = bpt.order_no\n\t\tWHERE blo.status = ?\n\t\t  AND bpt.type = ?\n\t\t  AND bpt.status = ?\n\t\t  AND bpt.channel_transaction_no != ''\n\t\t  AND bpt.channel_transaction_no IS NOT NULL\n\t\t  AND bpt.deleted_at IS NULL\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0 DISBURSEMENT 1]","duration":"40.0265ms","duration_ms":40}
{"level":"dev.info","ts":"[2025-08-14 09:43:00.027]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tid,\n\t\t\ttransaction_no,\n\t\t\torder_no,\n\t\t\tamount,\n\t\t\tcreated_at\n\t\tFROM business_payment_transactions \n\t\tWHERE type = ?\n\t\t  AND status = ?\n\t\t  AND deleted_at IS NULL\n\t\tORDER BY id ASC \n\t\tLIMIT 100\n\t, [REFUND 1]","duration":"25.6802ms","duration_ms":25}
{"level":"dev.info","ts":"[2025-08-14 09:43:00.045]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tc.channel_name,\n\t\t\tc.auto_label\n\t\tFROM business_loan_orders blo\n\t\tLEFT JOIN channel c ON blo.channel_id = c.id\n\t\tWHERE blo.status = ?\n\t\t  AND blo.risk_control_results = 0\n\t\t  AND c.auto_label = 0\n\t\t  AND c.channel_status = 1\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0]","duration":"43.8458ms","duration_ms":43}
{"level":"dev.info","ts":"[2025-08-14 09:43:07.360]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b7eb3104b13a00ed9f10c","sql":"SELECT count(*) as count FROM business_payment_transactions pt LEFT JOIN business_repayment_bills rb ON pt.bill_id = rb.id  LEFT JOIN business_loan_orders lo ON pt.order_id = lo.id  LEFT JOIN business_app_account aa ON pt.user_id = aa.id WHERE pt.status = ? and (pt.deleted_at IS NULL) LIMIT 1, [2]","duration":"28.7539ms","duration_ms":28}
{"level":"dev.info","ts":"[2025-08-14 09:43:07.360]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b7eb3104b13a00ed9f10c","sql":"SELECT \n\t\tSUM(CASE WHEN pt.type IN ('REPAYMENT', 'WITHHOLD', 'MANUAL_WITHHOLD', 'PARTIAL_OFFLINE_REPAYMENT') THEN pt.amount ELSE 0 END) as total_income,\n\t\tSUM(CASE WHEN pt.type = 'REFUND' THEN pt.amount ELSE 0 END) as total_refund,\n\t\tSUM(CASE WHEN pt.type IN ('REPAYMENT', 'WITHHOLD', 'MANUAL_WITHHOLD', 'PARTIAL_OFFLINE_REPAYMENT')\n\t\t\t AND DATE(pt.completed_at) = DATE(rb.due_date) THEN pt.amount ELSE 0 END) as due_income,\n\t\tSUM(CASE WHEN pt.type IN ('REPAYMENT', 'WITHHOLD', 'MANUAL_WITHHOLD', 'PARTIAL_OFFLINE_REPAYMENT')\n\t\t\t AND pt.completed_at > rb.due_date THEN pt.amount ELSE 0 END) as overdue_income,\n\t\tSUM(CASE WHEN pt.type IN ('REPAYMENT', 'WITHHOLD', 'MANUAL_WITHHOLD', 'PARTIAL_OFFLINE_REPAYMENT')\n\t\t\t AND pt.completed_at < rb.due_date THEN pt.amount ELSE 0 END) as early_income,\n\t\tCOUNT(DISTINCT pt.user_id) as total_customers,\n\t\tCOUNT(DISTINCT pt.order_id) as total_orders\n\t FROM business_payment_transactions pt LEFT JOIN business_repayment_bills rb ON pt.bill_id = rb.id  LEFT JOIN business_loan_orders lo ON pt.order_id = lo.id  LEFT JOIN business_app_account aa ON pt.user_id = aa.id WHERE pt.status = ? and (pt.deleted_at IS NULL) LIMIT 1, [2]","duration":"28.7539ms","duration_ms":28}
{"level":"dev.info","ts":"[2025-08-14 09:43:07.377]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b7eb3104b13a00ed9f10c","sql":"SELECT \n\t\t\tpt.id,\n\t\t\tpt.transaction_no,\n\t\t\tlo.order_no,\n\t\t\taa.name as user_name,\n\t\t\taa.mobile,\n\t\t\tpt.type,\n\t\t\tpt.withhold_type,\n\t\t\tpt.offline_payment_channel_detail,\n\t\t\tpt.amount,\n\t\t\trb.period_number,\n\t\t\tpt.completed_at,\n\t\t\trb.due_date\n\t\t FROM business_payment_transactions pt LEFT JOIN business_repayment_bills rb ON pt.bill_id = rb.id  LEFT JOIN business_loan_orders lo ON pt.order_id = lo.id  LEFT JOIN business_app_account aa ON pt.user_id = aa.id WHERE pt.status = ? and (pt.deleted_at IS NULL) ORDER BY pt.completed_at DESC LIMIT 10, [2]","duration":"16.8252ms","duration_ms":16}
{"level":"dev.info","ts":"[2025-08-14 09:43:16.656]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b7eb53b2e49643bb8ee4d","sql":"SELECT count(*) as count FROM business_payment_transactions pt LEFT JOIN business_repayment_bills rb ON pt.bill_id = rb.id  LEFT JOIN business_loan_orders lo ON pt.order_id = lo.id  LEFT JOIN business_app_account aa ON pt.user_id = aa.id WHERE pt.status = ? and (pt.deleted_at IS NULL) LIMIT 1, [2]","duration":"18.3896ms","duration_ms":18}
{"level":"dev.info","ts":"[2025-08-14 09:43:16.656]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b7eb53b2e49643bb8ee4d","sql":"SELECT \n\t\tSUM(CASE WHEN pt.type IN ('REPAYMENT', 'WITHHOLD', 'MANUAL_WITHHOLD', 'PARTIAL_OFFLINE_REPAYMENT') THEN pt.amount ELSE 0 END) as total_income,\n\t\tSUM(CASE WHEN pt.type = 'REFUND' THEN pt.amount ELSE 0 END) as total_refund,\n\t\tSUM(CASE WHEN pt.type IN ('REPAYMENT', 'WITHHOLD', 'MANUAL_WITHHOLD', 'PARTIAL_OFFLINE_REPAYMENT')\n\t\t\t AND DATE(pt.completed_at) = DATE(rb.due_date) THEN pt.amount ELSE 0 END) as due_income,\n\t\tSUM(CASE WHEN pt.type IN ('REPAYMENT', 'WITHHOLD', 'MANUAL_WITHHOLD', 'PARTIAL_OFFLINE_REPAYMENT')\n\t\t\t AND pt.completed_at > rb.due_date THEN pt.amount ELSE 0 END) as overdue_income,\n\t\tSUM(CASE WHEN pt.type IN ('REPAYMENT', 'WITHHOLD', 'MANUAL_WITHHOLD', 'PARTIAL_OFFLINE_REPAYMENT')\n\t\t\t AND pt.completed_at < rb.due_date THEN pt.amount ELSE 0 END) as early_income,\n\t\tCOUNT(DISTINCT pt.user_id) as total_customers,\n\t\tCOUNT(DISTINCT pt.order_id) as total_orders\n\t FROM business_payment_transactions pt LEFT JOIN business_repayment_bills rb ON pt.bill_id = rb.id  LEFT JOIN business_loan_orders lo ON pt.order_id = lo.id  LEFT JOIN business_app_account aa ON pt.user_id = aa.id WHERE pt.status = ? and (pt.deleted_at IS NULL) LIMIT 1, [2]","duration":"18.3896ms","duration_ms":18}
{"level":"dev.info","ts":"[2025-08-14 09:43:16.692]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b7eb53b2e49643bb8ee4d","sql":"SELECT \n\t\t\tpt.id,\n\t\t\tpt.transaction_no,\n\t\t\tlo.order_no,\n\t\t\taa.name as user_name,\n\t\t\taa.mobile,\n\t\t\tpt.type,\n\t\t\tpt.withhold_type,\n\t\t\tpt.offline_payment_channel_detail,\n\t\t\tpt.amount,\n\t\t\trb.period_number,\n\t\t\tpt.completed_at,\n\t\t\trb.due_date\n\t\t FROM business_payment_transactions pt LEFT JOIN business_repayment_bills rb ON pt.bill_id = rb.id  LEFT JOIN business_loan_orders lo ON pt.order_id = lo.id  LEFT JOIN business_app_account aa ON pt.user_id = aa.id WHERE pt.status = ? and (pt.deleted_at IS NULL) ORDER BY pt.completed_at DESC LIMIT 10, [2]","duration":"35.5404ms","duration_ms":35}
{"level":"dev.info","ts":"[2025-08-14 09:43:25.173]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b7eb7360723dc942e0486","sql":"SELECT count(*) as count FROM business_payment_transactions pt LEFT JOIN business_repayment_bills rb ON pt.bill_id = rb.id  LEFT JOIN business_loan_orders lo ON pt.order_id = lo.id  LEFT JOIN business_app_account aa ON pt.user_id = aa.id WHERE pt.status = ? and (pt.deleted_at IS NULL) LIMIT 1, [2]","duration":"30.3896ms","duration_ms":30}
{"level":"dev.info","ts":"[2025-08-14 09:43:25.180]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b7eb7360723dc942e0486","sql":"SELECT \n\t\tSUM(CASE WHEN pt.type IN ('REPAYMENT', 'WITHHOLD', 'MANUAL_WITHHOLD', 'PARTIAL_OFFLINE_REPAYMENT') THEN pt.amount ELSE 0 END) as total_income,\n\t\tSUM(CASE WHEN pt.type = 'REFUND' THEN pt.amount ELSE 0 END) as total_refund,\n\t\tSUM(CASE WHEN pt.type IN ('REPAYMENT', 'WITHHOLD', 'MANUAL_WITHHOLD', 'PARTIAL_OFFLINE_REPAYMENT')\n\t\t\t AND DATE(pt.completed_at) = DATE(rb.due_date) THEN pt.amount ELSE 0 END) as due_income,\n\t\tSUM(CASE WHEN pt.type IN ('REPAYMENT', 'WITHHOLD', 'MANUAL_WITHHOLD', 'PARTIAL_OFFLINE_REPAYMENT')\n\t\t\t AND pt.completed_at > rb.due_date THEN pt.amount ELSE 0 END) as overdue_income,\n\t\tSUM(CASE WHEN pt.type IN ('REPAYMENT', 'WITHHOLD', 'MANUAL_WITHHOLD', 'PARTIAL_OFFLINE_REPAYMENT')\n\t\t\t AND pt.completed_at < rb.due_date THEN pt.amount ELSE 0 END) as early_income,\n\t\tCOUNT(DISTINCT pt.user_id) as total_customers,\n\t\tCOUNT(DISTINCT pt.order_id) as total_orders\n\t FROM business_payment_transactions pt LEFT JOIN business_repayment_bills rb ON pt.bill_id = rb.id  LEFT JOIN business_loan_orders lo ON pt.order_id = lo.id  LEFT JOIN business_app_account aa ON pt.user_id = aa.id WHERE pt.status = ? and (pt.deleted_at IS NULL) LIMIT 1, [2]","duration":"38.0009ms","duration_ms":38}
{"level":"dev.info","ts":"[2025-08-14 09:43:25.190]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b7eb7360723dc942e0486","sql":"SELECT \n\t\t\tpt.id,\n\t\t\tpt.transaction_no,\n\t\t\tlo.order_no,\n\t\t\taa.name as user_name,\n\t\t\taa.mobile,\n\t\t\tpt.type,\n\t\t\tpt.withhold_type,\n\t\t\tpt.offline_payment_channel_detail,\n\t\t\tpt.amount,\n\t\t\trb.period_number,\n\t\t\tpt.completed_at,\n\t\t\trb.due_date\n\t\t FROM business_payment_transactions pt LEFT JOIN business_repayment_bills rb ON pt.bill_id = rb.id  LEFT JOIN business_loan_orders lo ON pt.order_id = lo.id  LEFT JOIN business_app_account aa ON pt.user_id = aa.id WHERE pt.status = ? and (pt.deleted_at IS NULL) ORDER BY pt.completed_at DESC LIMIT 10, [2]","duration":"17.1161ms","duration_ms":17}
{"level":"dev.info","ts":"[2025-08-14 09:43:36.161]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b7eb9c40b518853a469ae","sql":"SELECT count(*) as count FROM business_payment_transactions pt LEFT JOIN business_repayment_bills rb ON pt.bill_id = rb.id  LEFT JOIN business_loan_orders lo ON pt.order_id = lo.id  LEFT JOIN business_app_account aa ON pt.user_id = aa.id WHERE pt.status = ? and (pt.deleted_at IS NULL) and pt.type IN (?,?,?,?) and pt.withhold_type = ? and pt.type = ? LIMIT 1, [2 REPAYMENT WITHHOLD MANUAL_WITHHOLD PARTIAL_OFFLINE_REPAYMENT GUARANTEE WITHHOLD]","duration":"46.7521ms","duration_ms":46}
{"level":"dev.info","ts":"[2025-08-14 09:43:36.162]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b7eb9c40b518853a469ae","sql":"SELECT \n\t\tSUM(CASE WHEN pt.type IN ('REPAYMENT', 'WITHHOLD', 'MANUAL_WITHHOLD', 'PARTIAL_OFFLINE_REPAYMENT') THEN pt.amount ELSE 0 END) as total_income,\n\t\tSUM(CASE WHEN pt.type = 'REFUND' THEN pt.amount ELSE 0 END) as total_refund,\n\t\tSUM(CASE WHEN pt.type IN ('REPAYMENT', 'WITHHOLD', 'MANUAL_WITHHOLD', 'PARTIAL_OFFLINE_REPAYMENT')\n\t\t\t AND DATE(pt.completed_at) = DATE(rb.due_date) THEN pt.amount ELSE 0 END) as due_income,\n\t\tSUM(CASE WHEN pt.type IN ('REPAYMENT', 'WITHHOLD', 'MANUAL_WITHHOLD', 'PARTIAL_OFFLINE_REPAYMENT')\n\t\t\t AND pt.completed_at > rb.due_date THEN pt.amount ELSE 0 END) as overdue_income,\n\t\tSUM(CASE WHEN pt.type IN ('REPAYMENT', 'WITHHOLD', 'MANUAL_WITHHOLD', 'PARTIAL_OFFLINE_REPAYMENT')\n\t\t\t AND pt.completed_at < rb.due_date THEN pt.amount ELSE 0 END) as early_income,\n\t\tCOUNT(DISTINCT pt.user_id) as total_customers,\n\t\tCOUNT(DISTINCT pt.order_id) as total_orders\n\t FROM business_payment_transactions pt LEFT JOIN business_repayment_bills rb ON pt.bill_id = rb.id  LEFT JOIN business_loan_orders lo ON pt.order_id = lo.id  LEFT JOIN business_app_account aa ON pt.user_id = aa.id WHERE pt.status = ? and (pt.deleted_at IS NULL) and pt.type IN (?,?,?,?) and pt.withhold_type = ? and pt.type = ? LIMIT 1, [2 REPAYMENT WITHHOLD MANUAL_WITHHOLD PARTIAL_OFFLINE_REPAYMENT GUARANTEE WITHHOLD]","duration":"48.1222ms","duration_ms":48}
{"level":"dev.info","ts":"[2025-08-14 09:43:36.178]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b7eb9c40b518853a469ae","sql":"SELECT \n\t\t\tpt.id,\n\t\t\tpt.transaction_no,\n\t\t\tlo.order_no,\n\t\t\taa.name as user_name,\n\t\t\taa.mobile,\n\t\t\tpt.type,\n\t\t\tpt.withhold_type,\n\t\t\tpt.offline_payment_channel_detail,\n\t\t\tpt.amount,\n\t\t\trb.period_number,\n\t\t\tpt.completed_at,\n\t\t\trb.due_date\n\t\t FROM business_payment_transactions pt LEFT JOIN business_repayment_bills rb ON pt.bill_id = rb.id  LEFT JOIN business_loan_orders lo ON pt.order_id = lo.id  LEFT JOIN business_app_account aa ON pt.user_id = aa.id WHERE pt.status = ? and (pt.deleted_at IS NULL) and pt.type IN (?,?,?,?) and pt.withhold_type = ? and pt.type = ? ORDER BY pt.completed_at DESC LIMIT 5, [2 REPAYMENT WITHHOLD MANUAL_WITHHOLD PARTIAL_OFFLINE_REPAYMENT GUARANTEE WITHHOLD]","duration":"15.371ms","duration_ms":15}
{"level":"dev.info","ts":"[2025-08-14 09:44:00.116]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT DISTINCT\n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tbpt.transaction_no,\n\t\t\tbpt.channel_transaction_no,\n\t\t\tbpt.status as transaction_status\n\t\tFROM business_loan_orders blo\n\t\tINNER JOIN business_payment_transactions bpt ON blo.order_no = bpt.order_no\n\t\tWHERE blo.status = ?\n\t\t  AND bpt.type = ?\n\t\t  AND bpt.status = ?\n\t\t  AND bpt.channel_transaction_no != ''\n\t\t  AND bpt.channel_transaction_no IS NOT NULL\n\t\t  AND bpt.deleted_at IS NULL\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0 DISBURSEMENT 1]","duration":"116.1702ms","duration_ms":116}
{"level":"dev.info","ts":"[2025-08-14 09:44:00.120]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tid,\n\t\t\ttransaction_no,\n\t\t\torder_no,\n\t\t\tamount,\n\t\t\tcreated_at\n\t\tFROM business_payment_transactions \n\t\tWHERE type = ?\n\t\t  AND status = ?\n\t\t  AND deleted_at IS NULL\n\t\tORDER BY id ASC \n\t\tLIMIT 100\n\t, [REFUND 1]","duration":"118.6907ms","duration_ms":118}
{"level":"dev.info","ts":"[2025-08-14 09:44:00.120]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tc.channel_name,\n\t\t\tc.auto_label\n\t\tFROM business_loan_orders blo\n\t\tLEFT JOIN channel c ON blo.channel_id = c.id\n\t\tWHERE blo.status = ?\n\t\t  AND blo.risk_control_results = 0\n\t\t  AND c.auto_label = 0\n\t\t  AND c.channel_status = 1\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0]","duration":"119.2162ms","duration_ms":119}
{"level":"dev.info","ts":"[2025-08-14 09:44:00.120]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT DISTINCT\n\t\t\tbrb.id as bill_id,\n\t\t\tbrb.order_id,\n\t\t\tbrb.user_id,\n\t\t\tbrb.period_number,\n\t\t\tbrb.status as bill_status,\n\t\t\tbrb.total_due_amount,\n\t\t\tbrb.paid_amount,\n\t\t\tbpt.transaction_no,\n\t\t\tbpt.type as transaction_type,\n\t\t\tbpt.status as transaction_status,\n\t\t\tbpt.amount,\n\t\t\tbpt.third_party_order_no,\n\t\t\tbpt.created_at as transaction_created_at\n\t\tFROM business_repayment_bills brb\n\t\tINNER JOIN business_payment_transactions bpt ON brb.id = bpt.bill_id\n\t\tWHERE brb.status IN (?, ?, ?, ?)\n\t\t  AND bpt.status = ?\n\t\t  AND bpt.type IN (?, ?, ?)\n\t\t  AND bpt.transaction_no != ''\n\t\t  AND bpt.transaction_no IS NOT NULL\n\t\t  AND bpt.deleted_at IS NULL\n\t\tORDER BY bpt.created_at ASC\n\t\tLIMIT 100\n\t, [0 3 7 9 1 REPAYMENT WITHHOLD MANUAL_WITHHOLD]","duration":"119.2162ms","duration_ms":119}
{"level":"dev.info","ts":"[2025-08-14 09:44:28.269]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b7ec5e78181580c06729b","sql":"SELECT COALESCE(SUM(loan_amount), 0) as total_amount, COUNT(*) as order_count, COALESCE(SUM(principal), 0) as principal_amount FROM `business_loan_orders` WHERE `status` IN (?,?) LIMIT 1, [1 3]","duration":"19.4223ms","duration_ms":19}
{"level":"dev.info","ts":"[2025-08-14 09:44:28.281]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b7ec5e78181580c06729b","sql":"SELECT COUNT(DISTINCT user_id) as customer_count FROM `business_loan_orders` WHERE `status` IN (?,?) LIMIT 1, [1 3]","duration":"12.3593ms","duration_ms":12}
{"level":"dev.info","ts":"[2025-08-14 09:44:28.292]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b7ec5e78181580c06729b","sql":"SELECT COALESCE(SUM(total_due_amount - total_waive_amount), 0) as due_amount,COALESCE(SUM(due_principal), 0) as due_principal_amount FROM `business_repayment_bills` WHERE DATE(due_date) <= ? LIMIT 1, [2025-08-14]","duration":"42.856ms","duration_ms":42}
{"level":"dev.info","ts":"[2025-08-14 09:44:28.292]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b7ec5e78181580c06729b","sql":"SELECT COUNT(DISTINCT user_id) as customer_count FROM `business_repayment_bills` WHERE DATE(due_date) < ? and `status` IN (?,?,?) LIMIT 1, [2025-08-14 3 9 2]","duration":"42.856ms","duration_ms":42}
{"level":"dev.info","ts":"[2025-08-14 09:44:28.301]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b7ec5e78181580c06729b","sql":"SELECT COALESCE(SUM(paid_amount), 0) as repayment_amount FROM `business_repayment_bills` WHERE DATE(due_date) <= ? LIMIT 1, [2025-08-14]","duration":"8.9567ms","duration_ms":8}
{"level":"dev.info","ts":"[2025-08-14 09:44:28.301]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b7ec5e78181580c06729b","sql":"SELECT COALESCE(SUM(total_due_amount - paid_amount - total_waive_amount), 0) as overdue_amount FROM `business_repayment_bills` WHERE DATE(due_date) < ? and `status` IN (?,?,?) LIMIT 1, [2025-08-14 3 9 2]","duration":"8.9567ms","duration_ms":8}
{"level":"dev.info","ts":"[2025-08-14 09:44:39.242]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b7ec875f47664032e4e45","sql":"SELECT COALESCE(SUM(total_due_amount - total_waive_amount), 0) as due_amount,COALESCE(SUM(due_principal), 0) as due_principal_amount FROM `business_repayment_bills` WHERE DATE(due_date) <= ? LIMIT 1, [2025-08-14]","duration":"14.0159ms","duration_ms":14}
{"level":"dev.info","ts":"[2025-08-14 09:44:39.242]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b7ec875f47664032e4e45","sql":"SELECT COUNT(DISTINCT user_id) as customer_count FROM `business_repayment_bills` WHERE DATE(due_date) < ? and `status` IN (?,?,?) LIMIT 1, [2025-08-14 3 9 2]","duration":"14.0159ms","duration_ms":14}
{"level":"dev.info","ts":"[2025-08-14 09:44:39.242]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b7ec875f47664032e4e45","sql":"SELECT COALESCE(SUM(loan_amount), 0) as total_amount, COUNT(*) as order_count, COALESCE(SUM(principal), 0) as principal_amount FROM `business_loan_orders` WHERE `status` IN (?,?) LIMIT 1, [1 3]","duration":"14.5307ms","duration_ms":14}
{"level":"dev.info","ts":"[2025-08-14 09:44:39.264]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b7ec875f47664032e4e45","sql":"SELECT COALESCE(SUM(total_due_amount - paid_amount - total_waive_amount), 0) as overdue_amount FROM `business_repayment_bills` WHERE DATE(due_date) < ? and `status` IN (?,?,?) LIMIT 1, [2025-08-14 3 9 2]","duration":"21.9636ms","duration_ms":21}
{"level":"dev.info","ts":"[2025-08-14 09:44:39.264]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b7ec875f47664032e4e45","sql":"SELECT COUNT(DISTINCT user_id) as customer_count FROM `business_loan_orders` WHERE `status` IN (?,?) LIMIT 1, [1 3]","duration":"20.9525ms","duration_ms":20}
{"level":"dev.info","ts":"[2025-08-14 09:44:39.270]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b7ec875f47664032e4e45","sql":"SELECT COALESCE(SUM(paid_amount), 0) as repayment_amount FROM `business_repayment_bills` WHERE DATE(due_date) <= ? LIMIT 1, [2025-08-14]","duration":"27.1582ms","duration_ms":27}
{"level":"dev.info","ts":"[2025-08-14 09:45:28.355]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b7ed224c556dc942b0c27","sql":"SELECT COALESCE(SUM(loan_amount), 0) as total_amount, COUNT(*) as order_count, COALESCE(SUM(principal), 0) as principal_amount FROM `business_loan_orders` WHERE `status` IN (?,?) LIMIT 1, [1 3]","duration":"38.4638ms","duration_ms":38}
{"level":"dev.info","ts":"[2025-08-14 09:45:28.373]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b7ed224c556dc942b0c27","sql":"SELECT COUNT(DISTINCT user_id) as customer_count FROM `business_loan_orders` WHERE `status` IN (?,?) LIMIT 1, [1 3]","duration":"16.3639ms","duration_ms":16}
{"level":"dev.info","ts":"[2025-08-14 09:45:28.402]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b7ed224c556dc942b0c27","sql":"SELECT COALESCE(SUM(total_due_amount - total_waive_amount), 0) as due_amount,COALESCE(SUM(due_principal), 0) as due_principal_amount FROM `business_repayment_bills` WHERE DATE(due_date) <= ? LIMIT 1, [2025-08-14]","duration":"84.8776ms","duration_ms":84}
{"level":"dev.info","ts":"[2025-08-14 09:45:28.402]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b7ed224c556dc942b0c27","sql":"SELECT COUNT(DISTINCT user_id) as customer_count FROM `business_repayment_bills` WHERE DATE(due_date) < ? and `status` IN (?,?,?) LIMIT 1, [2025-08-14 3 9 2]","duration":"85.3873ms","duration_ms":85}
{"level":"dev.info","ts":"[2025-08-14 09:45:28.417]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b7ed224c556dc942b0c27","sql":"SELECT COALESCE(SUM(total_due_amount - paid_amount - total_waive_amount), 0) as overdue_amount FROM `business_repayment_bills` WHERE DATE(due_date) < ? and `status` IN (?,?,?) LIMIT 1, [2025-08-14 3 9 2]","duration":"14.6245ms","duration_ms":14}
{"level":"dev.info","ts":"[2025-08-14 09:45:28.417]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b7ed224c556dc942b0c27","sql":"SELECT COALESCE(SUM(paid_amount), 0) as repayment_amount FROM `business_repayment_bills` WHERE DATE(due_date) <= ? LIMIT 1, [2025-08-14]","duration":"15.1342ms","duration_ms":15}
{"level":"dev.info","ts":"[2025-08-14 09:46:32.363]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b7ee12b70f1947f5016d3","sql":"SELECT COALESCE(SUM(total_due_amount - total_waive_amount), 0) as due_amount,COALESCE(SUM(due_principal), 0) as due_principal_amount FROM `business_repayment_bills` WHERE DATE(due_date) BETWEEN ? and ? LIMIT 1, [Y-m-d Y-m-d]","duration":"13.4536ms","duration_ms":13}
{"level":"dev.info","ts":"[2025-08-14 09:46:32.386]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b7ee12b70f1947f5016d3","sql":"SELECT COALESCE(SUM(paid_amount), 0) as repayment_amount FROM `business_repayment_bills` WHERE DATE(due_date) BETWEEN ? and ? LIMIT 1, [Y-m-d Y-m-d]","duration":"22.0548ms","duration_ms":22}
{"level":"dev.info","ts":"[2025-08-14 09:46:32.397]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b7ee12b70f1947f5016d3","sql":"SELECT COALESCE(SUM(loan_amount), 0) as total_amount, COUNT(*) as order_count, COALESCE(SUM(principal), 0) as principal_amount FROM `business_loan_orders` WHERE `status` IN (?,?) and DATE(disbursed_at) BETWEEN ? and ? LIMIT 1, [1 3 Y-m-d Y-m-d]","duration":"47.1718ms","duration_ms":47}
{"level":"dev.info","ts":"[2025-08-14 09:46:32.397]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b7ee12b70f1947f5016d3","sql":"SELECT COUNT(DISTINCT user_id) as customer_count FROM `business_repayment_bills` WHERE DATE(due_date) < ? and `status` IN (?,?,?) LIMIT 1, [2025-08-14 3 9 2]","duration":"47.4028ms","duration_ms":47}
{"level":"dev.info","ts":"[2025-08-14 09:46:32.442]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b7ee12b70f1947f5016d3","sql":"SELECT COUNT(DISTINCT user_id) as customer_count FROM `business_loan_orders` WHERE `status` IN (?,?) and DATE(disbursed_at) BETWEEN ? and ? LIMIT 1, [1 3 Y-m-d Y-m-d]","duration":"45.0625ms","duration_ms":45}
{"level":"dev.info","ts":"[2025-08-14 09:46:32.443]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b7ee12b70f1947f5016d3","sql":"SELECT COALESCE(SUM(total_due_amount - paid_amount - total_waive_amount), 0) as overdue_amount FROM `business_repayment_bills` WHERE DATE(due_date) < ? and `status` IN (?,?,?) LIMIT 1, [2025-08-14 3 9 2]","duration":"45.8002ms","duration_ms":45}
{"level":"dev.info","ts":"[2025-08-14 09:46:45.266]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b7ee5ccf223f43488aaf5","sql":"SELECT COALESCE(SUM(total_due_amount - total_waive_amount), 0) as due_amount,COALESCE(SUM(due_principal), 0) as due_principal_amount FROM `business_repayment_bills` WHERE DATE(due_date) BETWEEN ? and ? LIMIT 1, [Y-m-d Y-m-d]","duration":"24.2057ms","duration_ms":24}
{"level":"dev.info","ts":"[2025-08-14 09:46:45.266]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b7ee5ccf223f43488aaf5","sql":"SELECT COUNT(DISTINCT user_id) as customer_count FROM `business_repayment_bills` WHERE DATE(due_date) < ? and `status` IN (?,?,?) LIMIT 1, [2025-08-14 3 9 2]","duration":"24.2057ms","duration_ms":24}
{"level":"dev.info","ts":"[2025-08-14 09:46:45.266]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b7ee5ccf223f43488aaf5","sql":"SELECT COALESCE(SUM(loan_amount), 0) as total_amount, COUNT(*) as order_count, COALESCE(SUM(principal), 0) as principal_amount FROM `business_loan_orders` WHERE `status` IN (?,?) and DATE(disbursed_at) BETWEEN ? and ? LIMIT 1, [1 3 Y-m-d Y-m-d]","duration":"24.2057ms","duration_ms":24}
{"level":"dev.info","ts":"[2025-08-14 09:46:45.277]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b7ee5ccf223f43488aaf5","sql":"SELECT COALESCE(SUM(paid_amount), 0) as repayment_amount FROM `business_repayment_bills` WHERE DATE(due_date) BETWEEN ? and ? LIMIT 1, [Y-m-d Y-m-d]","duration":"10.939ms","duration_ms":10}
{"level":"dev.info","ts":"[2025-08-14 09:46:45.277]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b7ee5ccf223f43488aaf5","sql":"SELECT COUNT(DISTINCT user_id) as customer_count FROM `business_loan_orders` WHERE `status` IN (?,?) and DATE(disbursed_at) BETWEEN ? and ? LIMIT 1, [1 3 Y-m-d Y-m-d]","duration":"10.939ms","duration_ms":10}
{"level":"dev.info","ts":"[2025-08-14 09:46:45.277]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b7ee5ccf223f43488aaf5","sql":"SELECT COALESCE(SUM(total_due_amount - paid_amount - total_waive_amount), 0) as overdue_amount FROM `business_repayment_bills` WHERE DATE(due_date) < ? and `status` IN (?,?,?) LIMIT 1, [2025-08-14 3 9 2]","duration":"10.939ms","duration_ms":10}
{"level":"dev.info","ts":"[2025-08-14 09:46:51.186]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b7ee72db72d3ca918c3c4","sql":"SELECT COUNT(DISTINCT user_id) as customer_count FROM `business_repayment_bills` WHERE DATE(due_date) < ? and `status` IN (?,?,?) LIMIT 1, [2025-08-14 3 9 2]","duration":"25.8879ms","duration_ms":25}
{"level":"dev.info","ts":"[2025-08-14 09:46:51.199]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b7ee72db72d3ca918c3c4","sql":"SELECT COALESCE(SUM(total_due_amount - total_waive_amount), 0) as due_amount,COALESCE(SUM(due_principal), 0) as due_principal_amount FROM `business_repayment_bills` WHERE DATE(due_date) BETWEEN ? and ? LIMIT 1, [Y-m-d Y-m-d]","duration":"38.2216ms","duration_ms":38}
{"level":"dev.info","ts":"[2025-08-14 09:46:51.199]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b7ee72db72d3ca918c3c4","sql":"SELECT COALESCE(SUM(loan_amount), 0) as total_amount, COUNT(*) as order_count, COALESCE(SUM(principal), 0) as principal_amount FROM `business_loan_orders` WHERE `status` IN (?,?) and DATE(disbursed_at) BETWEEN ? and ? LIMIT 1, [1 3 Y-m-d Y-m-d]","duration":"38.2216ms","duration_ms":38}
{"level":"dev.info","ts":"[2025-08-14 09:46:51.204]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b7ee72db72d3ca918c3c4","sql":"SELECT COALESCE(SUM(total_due_amount - paid_amount - total_waive_amount), 0) as overdue_amount FROM `business_repayment_bills` WHERE DATE(due_date) < ? and `status` IN (?,?,?) LIMIT 1, [2025-08-14 3 9 2]","duration":"16.5866ms","duration_ms":16}
{"level":"dev.info","ts":"[2025-08-14 09:46:51.225]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b7ee72db72d3ca918c3c4","sql":"SELECT COALESCE(SUM(paid_amount), 0) as repayment_amount FROM `business_repayment_bills` WHERE DATE(due_date) BETWEEN ? and ? LIMIT 1, [Y-m-d Y-m-d]","duration":"25.5016ms","duration_ms":25}
{"level":"dev.info","ts":"[2025-08-14 09:46:51.225]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b7ee72db72d3ca918c3c4","sql":"SELECT COUNT(DISTINCT user_id) as customer_count FROM `business_loan_orders` WHERE `status` IN (?,?) and DATE(disbursed_at) BETWEEN ? and ? LIMIT 1, [1 3 Y-m-d Y-m-d]","duration":"25.5016ms","duration_ms":25}
{"level":"dev.info","ts":"[2025-08-14 09:47:00.016]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tid,\n\t\t\ttransaction_no,\n\t\t\torder_no,\n\t\t\tamount,\n\t\t\tcreated_at\n\t\tFROM business_payment_transactions \n\t\tWHERE type = ?\n\t\t  AND status = ?\n\t\t  AND deleted_at IS NULL\n\t\tORDER BY id ASC \n\t\tLIMIT 100\n\t, [REFUND 1]","duration":"15.563ms","duration_ms":15}
{"level":"dev.info","ts":"[2025-08-14 09:47:00.016]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tc.channel_name,\n\t\t\tc.auto_label\n\t\tFROM business_loan_orders blo\n\t\tLEFT JOIN channel c ON blo.channel_id = c.id\n\t\tWHERE blo.status = ?\n\t\t  AND blo.risk_control_results = 0\n\t\t  AND c.auto_label = 0\n\t\t  AND c.channel_status = 1\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0]","duration":"16.0671ms","duration_ms":16}
{"level":"dev.info","ts":"[2025-08-14 09:47:00.973]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b7ee974428574d5f0879f","sql":"SELECT COUNT(DISTINCT user_id) as customer_count FROM `business_repayment_bills` WHERE DATE(due_date) < ? and `status` IN (?,?,?) LIMIT 1, [2025-08-14 3 9 2]","duration":"38.7833ms","duration_ms":38}
{"level":"dev.info","ts":"[2025-08-14 09:47:00.974]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b7ee974428574d5f0879f","sql":"SELECT COALESCE(SUM(total_due_amount - total_waive_amount), 0) as due_amount,COALESCE(SUM(due_principal), 0) as due_principal_amount FROM `business_repayment_bills` WHERE DATE(due_date) BETWEEN ? and ? LIMIT 1, [Y-m-d Y-m-d]","duration":"39.9497ms","duration_ms":39}
{"level":"dev.info","ts":"[2025-08-14 09:47:00.974]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b7ee974428574d5f0879f","sql":"SELECT COALESCE(SUM(loan_amount), 0) as total_amount, COUNT(*) as order_count, COALESCE(SUM(principal), 0) as principal_amount FROM `business_loan_orders` WHERE `status` IN (?,?) and DATE(disbursed_at) BETWEEN ? and ? LIMIT 1, [1 3 Y-m-d Y-m-d]","duration":"39.9497ms","duration_ms":39}
{"level":"dev.info","ts":"[2025-08-14 09:47:00.989]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b7ee974428574d5f0879f","sql":"SELECT COALESCE(SUM(total_due_amount - paid_amount - total_waive_amount), 0) as overdue_amount FROM `business_repayment_bills` WHERE DATE(due_date) < ? and `status` IN (?,?,?) LIMIT 1, [2025-08-14 3 9 2]","duration":"14.8121ms","duration_ms":14}
{"level":"dev.info","ts":"[2025-08-14 09:47:00.995]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b7ee974428574d5f0879f","sql":"SELECT COALESCE(SUM(paid_amount), 0) as repayment_amount FROM `business_repayment_bills` WHERE DATE(due_date) BETWEEN ? and ? LIMIT 1, [Y-m-d Y-m-d]","duration":"20.1587ms","duration_ms":20}
{"level":"dev.info","ts":"[2025-08-14 09:47:00.995]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b7ee974428574d5f0879f","sql":"SELECT COUNT(DISTINCT user_id) as customer_count FROM `business_loan_orders` WHERE `status` IN (?,?) and DATE(disbursed_at) BETWEEN ? and ? LIMIT 1, [1 3 Y-m-d Y-m-d]","duration":"20.1587ms","duration_ms":20}
{"level":"dev.info","ts":"[2025-08-14 09:47:04.670]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b7eea51cfbb505470f968","sql":"SELECT COUNT(DISTINCT user_id) as customer_count FROM `business_repayment_bills` WHERE DATE(due_date) < ? and `status` IN (?,?,?) LIMIT 1, [2025-08-14 3 9 2]","duration":"19.0673ms","duration_ms":19}
{"level":"dev.info","ts":"[2025-08-14 09:47:04.683]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b7eea51cfbb505470f968","sql":"SELECT COALESCE(SUM(loan_amount), 0) as total_amount, COUNT(*) as order_count, COALESCE(SUM(principal), 0) as principal_amount FROM `business_loan_orders` WHERE `status` IN (?,?) and DATE(disbursed_at) BETWEEN ? and ? LIMIT 1, [1 3 Y-m-d Y-m-d]","duration":"31.9183ms","duration_ms":31}
{"level":"dev.info","ts":"[2025-08-14 09:47:04.683]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b7eea51cfbb505470f968","sql":"SELECT COALESCE(SUM(total_due_amount - total_waive_amount), 0) as due_amount,COALESCE(SUM(due_principal), 0) as due_principal_amount FROM `business_repayment_bills` WHERE DATE(due_date) BETWEEN ? and ? LIMIT 1, [Y-m-d Y-m-d]","duration":"32.4211ms","duration_ms":32}
{"level":"dev.info","ts":"[2025-08-14 09:47:04.691]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b7eea51cfbb505470f968","sql":"SELECT COALESCE(SUM(total_due_amount - paid_amount - total_waive_amount), 0) as overdue_amount FROM `business_repayment_bills` WHERE DATE(due_date) < ? and `status` IN (?,?,?) LIMIT 1, [2025-08-14 3 9 2]","duration":"20.6013ms","duration_ms":20}
{"level":"dev.info","ts":"[2025-08-14 09:47:04.695]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b7eea51cfbb505470f968","sql":"SELECT COALESCE(SUM(paid_amount), 0) as repayment_amount FROM `business_repayment_bills` WHERE DATE(due_date) BETWEEN ? and ? LIMIT 1, [Y-m-d Y-m-d]","duration":"11.9637ms","duration_ms":11}
{"level":"dev.info","ts":"[2025-08-14 09:47:04.695]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b7eea51cfbb505470f968","sql":"SELECT COUNT(DISTINCT user_id) as customer_count FROM `business_loan_orders` WHERE `status` IN (?,?) and DATE(disbursed_at) BETWEEN ? and ? LIMIT 1, [1 3 Y-m-d Y-m-d]","duration":"11.9637ms","duration_ms":11}
{"level":"dev.info","ts":"[2025-08-14 09:47:13.324]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b7eec55b2c4a41b6c5468","sql":"SELECT COUNT(DISTINCT user_id) as customer_count FROM `business_repayment_bills` WHERE DATE(due_date) < ? and `status` IN (?,?,?) LIMIT 1, [2025-08-14 3 9 2]","duration":"17.4873ms","duration_ms":17}
{"level":"dev.info","ts":"[2025-08-14 09:47:13.324]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b7eec55b2c4a41b6c5468","sql":"SELECT COALESCE(SUM(loan_amount), 0) as total_amount, COUNT(*) as order_count, COALESCE(SUM(principal), 0) as principal_amount FROM `business_loan_orders` WHERE `status` IN (?,?) and DATE(disbursed_at) BETWEEN ? and ? LIMIT 1, [1 3 Y-m-d Y-m-d]","duration":"18.0387ms","duration_ms":18}
{"level":"dev.info","ts":"[2025-08-14 09:47:13.324]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b7eec55b2c4a41b6c5468","sql":"SELECT COALESCE(SUM(total_due_amount - total_waive_amount), 0) as due_amount,COALESCE(SUM(due_principal), 0) as due_principal_amount FROM `business_repayment_bills` WHERE DATE(due_date) BETWEEN ? and ? LIMIT 1, [Y-m-d Y-m-d]","duration":"18.0387ms","duration_ms":18}
{"level":"dev.info","ts":"[2025-08-14 09:47:13.335]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b7eec55b2c4a41b6c5468","sql":"SELECT COALESCE(SUM(paid_amount), 0) as repayment_amount FROM `business_repayment_bills` WHERE DATE(due_date) BETWEEN ? and ? LIMIT 1, [Y-m-d Y-m-d]","duration":"11.6187ms","duration_ms":11}
{"level":"dev.info","ts":"[2025-08-14 09:47:13.335]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b7eec55b2c4a41b6c5468","sql":"SELECT COUNT(DISTINCT user_id) as customer_count FROM `business_loan_orders` WHERE `status` IN (?,?) and DATE(disbursed_at) BETWEEN ? and ? LIMIT 1, [1 3 Y-m-d Y-m-d]","duration":"11.6187ms","duration_ms":11}
{"level":"dev.info","ts":"[2025-08-14 09:47:13.335]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b7eec55b2c4a41b6c5468","sql":"SELECT COALESCE(SUM(total_due_amount - paid_amount - total_waive_amount), 0) as overdue_amount FROM `business_repayment_bills` WHERE DATE(due_date) < ? and `status` IN (?,?,?) LIMIT 1, [2025-08-14 3 9 2]","duration":"11.6187ms","duration_ms":11}
{"level":"dev.info","ts":"[2025-08-14 09:47:14.467]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b7eec99eef9f8d74c9a5a","sql":"SELECT COALESCE(SUM(total_due_amount - total_waive_amount), 0) as due_amount,COALESCE(SUM(due_principal), 0) as due_principal_amount FROM `business_repayment_bills` WHERE DATE(due_date) BETWEEN ? and ? LIMIT 1, [Y-m-d Y-m-d]","duration":"14.6559ms","duration_ms":14}
{"level":"dev.info","ts":"[2025-08-14 09:47:14.468]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b7eec99eef9f8d74c9a5a","sql":"SELECT COALESCE(SUM(loan_amount), 0) as total_amount, COUNT(*) as order_count, COALESCE(SUM(principal), 0) as principal_amount FROM `business_loan_orders` WHERE `status` IN (?,?) and DATE(disbursed_at) BETWEEN ? and ? LIMIT 1, [1 3 Y-m-d Y-m-d]","duration":"15.9408ms","duration_ms":15}
{"level":"dev.info","ts":"[2025-08-14 09:47:14.468]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b7eec99eef9f8d74c9a5a","sql":"SELECT COUNT(DISTINCT user_id) as customer_count FROM `business_repayment_bills` WHERE DATE(due_date) < ? and `status` IN (?,?,?) LIMIT 1, [2025-08-14 3 9 2]","duration":"14.9146ms","duration_ms":14}
{"level":"dev.info","ts":"[2025-08-14 09:47:14.476]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b7eec99eef9f8d74c9a5a","sql":"SELECT COALESCE(SUM(paid_amount), 0) as repayment_amount FROM `business_repayment_bills` WHERE DATE(due_date) BETWEEN ? and ? LIMIT 1, [Y-m-d Y-m-d]","duration":"8.0275ms","duration_ms":8}
{"level":"dev.info","ts":"[2025-08-14 09:47:14.476]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b7eec99eef9f8d74c9a5a","sql":"SELECT COALESCE(SUM(total_due_amount - paid_amount - total_waive_amount), 0) as overdue_amount FROM `business_repayment_bills` WHERE DATE(due_date) < ? and `status` IN (?,?,?) LIMIT 1, [2025-08-14 3 9 2]","duration":"7.4875ms","duration_ms":7}
{"level":"dev.info","ts":"[2025-08-14 09:47:14.477]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b7eec99eef9f8d74c9a5a","sql":"SELECT COUNT(DISTINCT user_id) as customer_count FROM `business_loan_orders` WHERE `status` IN (?,?) and DATE(disbursed_at) BETWEEN ? and ? LIMIT 1, [1 3 Y-m-d Y-m-d]","duration":"9.2676ms","duration_ms":9}
{"level":"dev.info","ts":"[2025-08-14 09:47:38.475]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b7ef22fecdd6c84db6955","sql":"SELECT COALESCE(SUM(loan_amount), 0) as total_amount, COUNT(*) as order_count, COALESCE(SUM(principal), 0) as principal_amount FROM `business_loan_orders` WHERE `status` IN (?,?) and DATE(disbursed_at) BETWEEN ? and ? LIMIT 1, [1 3 Y-m-d Y-m-d]","duration":"33.4448ms","duration_ms":33}
{"level":"dev.info","ts":"[2025-08-14 09:47:38.475]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b7ef22fecdd6c84db6955","sql":"SELECT COALESCE(SUM(total_due_amount - total_waive_amount), 0) as due_amount,COALESCE(SUM(due_principal), 0) as due_principal_amount FROM `business_repayment_bills` WHERE DATE(due_date) BETWEEN ? and ? LIMIT 1, [Y-m-d Y-m-d]","duration":"33.4448ms","duration_ms":33}
{"level":"dev.info","ts":"[2025-08-14 09:47:38.475]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b7ef22fecdd6c84db6955","sql":"SELECT COUNT(DISTINCT user_id) as customer_count FROM `business_repayment_bills` WHERE DATE(due_date) < ? and `status` IN (?,?,?) LIMIT 1, [2025-08-14 3 9 2]","duration":"33.4448ms","duration_ms":33}
{"level":"dev.info","ts":"[2025-08-14 09:47:38.485]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b7ef22fecdd6c84db6955","sql":"SELECT COALESCE(SUM(paid_amount), 0) as repayment_amount FROM `business_repayment_bills` WHERE DATE(due_date) BETWEEN ? and ? LIMIT 1, [Y-m-d Y-m-d]","duration":"9.8592ms","duration_ms":9}
{"level":"dev.info","ts":"[2025-08-14 09:47:38.485]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b7ef22fecdd6c84db6955","sql":"SELECT COUNT(DISTINCT user_id) as customer_count FROM `business_loan_orders` WHERE `status` IN (?,?) and DATE(disbursed_at) BETWEEN ? and ? LIMIT 1, [1 3 Y-m-d Y-m-d]","duration":"9.8592ms","duration_ms":9}
{"level":"dev.info","ts":"[2025-08-14 09:47:38.485]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b7ef22fecdd6c84db6955","sql":"SELECT COALESCE(SUM(total_due_amount - paid_amount - total_waive_amount), 0) as overdue_amount FROM `business_repayment_bills` WHERE DATE(due_date) < ? and `status` IN (?,?,?) LIMIT 1, [2025-08-14 3 9 2]","duration":"9.8592ms","duration_ms":9}
{"level":"dev.info","ts":"[2025-08-14 09:48:05.080]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT COALESCE(SUM(loan_amount), 0) as total_amount, COUNT(*) as order_count, COALESCE(SUM(principal), 0) as principal_amount FROM `business_loan_orders` WHERE `status` IN (?,?) and DATE(disbursed_at) BETWEEN ? and ? LIMIT 1, [1 3 Y-m-d Y-m-d]","duration":"15.5216ms","duration_ms":15}
{"level":"dev.info","ts":"[2025-08-14 09:48:05.080]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT COALESCE(SUM(total_due_amount - total_waive_amount), 0) as due_amount,COALESCE(SUM(due_principal), 0) as due_principal_amount FROM `business_repayment_bills` WHERE DATE(due_date) BETWEEN ? and ? LIMIT 1, [Y-m-d Y-m-d]","duration":"15.5216ms","duration_ms":15}
{"level":"dev.info","ts":"[2025-08-14 09:48:05.080]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tid,\n\t\t\ttransaction_no,\n\t\t\torder_no,\n\t\t\tamount,\n\t\t\tcreated_at\n\t\tFROM business_payment_transactions \n\t\tWHERE type = ?\n\t\t  AND status = ?\n\t\t  AND deleted_at IS NULL\n\t\tORDER BY id ASC \n\t\tLIMIT 100\n\t, [REFUND 1]","duration":"15.0146ms","duration_ms":15}
{"level":"dev.info","ts":"[2025-08-14 09:48:05.094]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT COALESCE(SUM(paid_amount), 0) as repayment_amount FROM `business_repayment_bills` WHERE DATE(due_date) BETWEEN ? and ? LIMIT 1, [Y-m-d Y-m-d]","duration":"13.2177ms","duration_ms":13}
{"level":"dev.info","ts":"[2025-08-14 09:48:05.094]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT COUNT(DISTINCT user_id) as customer_count FROM `business_loan_orders` WHERE `status` IN (?,?) and DATE(disbursed_at) BETWEEN ? and ? LIMIT 1, [1 3 Y-m-d Y-m-d]","duration":"13.7988ms","duration_ms":13}
{"level":"dev.info","ts":"[2025-08-14 09:48:05.104]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT COUNT(DISTINCT user_id) as customer_count FROM `business_repayment_bills` WHERE DATE(due_date) < ? and `status` IN (?,?,?) LIMIT 1, [2025-08-14 3 9 2]","duration":"38.9845ms","duration_ms":38}
{"level":"dev.info","ts":"[2025-08-14 09:48:05.108]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tc.channel_name,\n\t\t\tc.auto_label\n\t\tFROM business_loan_orders blo\n\t\tLEFT JOIN channel c ON blo.channel_id = c.id\n\t\tWHERE blo.status = ?\n\t\t  AND blo.risk_control_results = 0\n\t\t  AND c.auto_label = 0\n\t\t  AND c.channel_status = 1\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0]","duration":"42.9105ms","duration_ms":42}
{"level":"dev.info","ts":"[2025-08-14 09:48:05.108]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT DISTINCT\n\t\t\tbrb.id as bill_id,\n\t\t\tbrb.order_id,\n\t\t\tbrb.user_id,\n\t\t\tbrb.period_number,\n\t\t\tbrb.status as bill_status,\n\t\t\tbrb.total_due_amount,\n\t\t\tbrb.paid_amount,\n\t\t\tbpt.transaction_no,\n\t\t\tbpt.type as transaction_type,\n\t\t\tbpt.status as transaction_status,\n\t\t\tbpt.amount,\n\t\t\tbpt.third_party_order_no,\n\t\t\tbpt.created_at as transaction_created_at\n\t\tFROM business_repayment_bills brb\n\t\tINNER JOIN business_payment_transactions bpt ON brb.id = bpt.bill_id\n\t\tWHERE brb.status IN (?, ?, ?, ?)\n\t\t  AND bpt.status = ?\n\t\t  AND bpt.type IN (?, ?, ?)\n\t\t  AND bpt.transaction_no != ''\n\t\t  AND bpt.transaction_no IS NOT NULL\n\t\t  AND bpt.deleted_at IS NULL\n\t\tORDER BY bpt.created_at ASC\n\t\tLIMIT 100\n\t, [0 3 7 9 1 REPAYMENT WITHHOLD MANUAL_WITHHOLD]","duration":"42.9105ms","duration_ms":42}
{"level":"dev.info","ts":"[2025-08-14 09:48:05.108]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT DISTINCT\n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tbpt.transaction_no,\n\t\t\tbpt.channel_transaction_no,\n\t\t\tbpt.status as transaction_status\n\t\tFROM business_loan_orders blo\n\t\tINNER JOIN business_payment_transactions bpt ON blo.order_no = bpt.order_no\n\t\tWHERE blo.status = ?\n\t\t  AND bpt.type = ?\n\t\t  AND bpt.status = ?\n\t\t  AND bpt.channel_transaction_no != ''\n\t\t  AND bpt.channel_transaction_no IS NOT NULL\n\t\t  AND bpt.deleted_at IS NULL\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0 DISBURSEMENT 1]","duration":"42.9105ms","duration_ms":42}
{"level":"dev.info","ts":"[2025-08-14 09:48:05.117]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT COALESCE(SUM(total_due_amount - paid_amount - total_waive_amount), 0) as overdue_amount FROM `business_repayment_bills` WHERE DATE(due_date) < ? and `status` IN (?,?,?) LIMIT 1, [2025-08-14 3 9 2]","duration":"13.3364ms","duration_ms":13}
{"level":"dev.info","ts":"[2025-08-14 09:49:00.013]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tc.channel_name,\n\t\t\tc.auto_label\n\t\tFROM business_loan_orders blo\n\t\tLEFT JOIN channel c ON blo.channel_id = c.id\n\t\tWHERE blo.status = ?\n\t\t  AND blo.risk_control_results = 0\n\t\t  AND c.auto_label = 0\n\t\t  AND c.channel_status = 1\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0]","duration":"11.058ms","duration_ms":11}
{"level":"dev.info","ts":"[2025-08-14 09:49:00.014]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tid,\n\t\t\ttransaction_no,\n\t\t\torder_no,\n\t\t\tamount,\n\t\t\tcreated_at\n\t\tFROM business_payment_transactions \n\t\tWHERE type = ?\n\t\t  AND status = ?\n\t\t  AND deleted_at IS NULL\n\t\tORDER BY id ASC \n\t\tLIMIT 100\n\t, [REFUND 1]","duration":"12.3264ms","duration_ms":12}
{"level":"dev.info","ts":"[2025-08-14 09:49:59.277]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b7f12f7a3053c5a417ba6","sql":"SELECT COALESCE(SUM(total_due_amount - total_waive_amount), 0) as due_amount,COALESCE(SUM(due_principal), 0) as due_principal_amount FROM `business_repayment_bills` WHERE DATE(due_date) BETWEEN ? and ? LIMIT 1, [Y-m-d Y-m-d]","duration":"45.2225ms","duration_ms":45}
{"level":"dev.info","ts":"[2025-08-14 09:49:59.303]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b7f12f7a3053c5a417ba6","sql":"SELECT COALESCE(SUM(paid_amount), 0) as repayment_amount FROM `business_repayment_bills` WHERE DATE(due_date) BETWEEN ? and ? LIMIT 1, [Y-m-d Y-m-d]","duration":"24.3757ms","duration_ms":24}
{"level":"dev.info","ts":"[2025-08-14 09:49:59.308]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b7f12f7a3053c5a417ba6","sql":"SELECT COALESCE(SUM(loan_amount), 0) as total_amount, COUNT(*) as order_count, COALESCE(SUM(principal), 0) as principal_amount FROM `business_loan_orders` WHERE `status` IN (?,?) and DATE(disbursed_at) = ? LIMIT 1, [1 3 Y-m-d]","duration":"76.2347ms","duration_ms":76}
{"level":"dev.info","ts":"[2025-08-14 09:49:59.310]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b7f12f7a3053c5a417ba6","sql":"SELECT COUNT(DISTINCT user_id) as customer_count FROM `business_repayment_bills` WHERE DATE(due_date) < ? and `status` IN (?,?,?) LIMIT 1, [2025-08-14 3 9 2]","duration":"76.2347ms","duration_ms":76}
{"level":"dev.info","ts":"[2025-08-14 09:49:59.330]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b7f12f7a3053c5a417ba6","sql":"SELECT COALESCE(SUM(total_due_amount - paid_amount - total_waive_amount), 0) as overdue_amount FROM `business_repayment_bills` WHERE DATE(due_date) < ? and `status` IN (?,?,?) LIMIT 1, [2025-08-14 3 9 2]","duration":"19.5784ms","duration_ms":19}
{"level":"dev.info","ts":"[2025-08-14 09:49:59.330]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b7f12f7a3053c5a417ba6","sql":"SELECT COUNT(DISTINCT user_id) as customer_count FROM `business_loan_orders` WHERE `status` IN (?,?) and DATE(disbursed_at) = ? LIMIT 1, [1 3 Y-m-d]","duration":"19.5784ms","duration_ms":19}
{"level":"dev.info","ts":"[2025-08-14 09:50:00.029]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tid,\n\t\t\ttransaction_no,\n\t\t\torder_no,\n\t\t\tamount,\n\t\t\tcreated_at\n\t\tFROM business_payment_transactions \n\t\tWHERE type = ?\n\t\t  AND status = ?\n\t\t  AND deleted_at IS NULL\n\t\tORDER BY id ASC \n\t\tLIMIT 100\n\t, [REFUND 1]","duration":"27.283ms","duration_ms":27}
{"level":"dev.info","ts":"[2025-08-14 09:50:00.036]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tc.channel_name,\n\t\t\tc.auto_label\n\t\tFROM business_loan_orders blo\n\t\tLEFT JOIN channel c ON blo.channel_id = c.id\n\t\tWHERE blo.status = ?\n\t\t  AND blo.risk_control_results = 0\n\t\t  AND c.auto_label = 0\n\t\t  AND c.channel_status = 1\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0]","duration":"34.2771ms","duration_ms":34}
{"level":"dev.info","ts":"[2025-08-14 09:50:00.042]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT DISTINCT\n\t\t\tbrb.id as bill_id,\n\t\t\tbrb.order_id,\n\t\t\tbrb.user_id,\n\t\t\tbrb.period_number,\n\t\t\tbrb.status as bill_status,\n\t\t\tbrb.total_due_amount,\n\t\t\tbrb.paid_amount,\n\t\t\tbpt.transaction_no,\n\t\t\tbpt.type as transaction_type,\n\t\t\tbpt.status as transaction_status,\n\t\t\tbpt.amount,\n\t\t\tbpt.third_party_order_no,\n\t\t\tbpt.created_at as transaction_created_at\n\t\tFROM business_repayment_bills brb\n\t\tINNER JOIN business_payment_transactions bpt ON brb.id = bpt.bill_id\n\t\tWHERE brb.status IN (?, ?, ?, ?)\n\t\t  AND bpt.status = ?\n\t\t  AND bpt.type IN (?, ?, ?)\n\t\t  AND bpt.transaction_no != ''\n\t\t  AND bpt.transaction_no IS NOT NULL\n\t\t  AND bpt.deleted_at IS NULL\n\t\tORDER BY bpt.created_at ASC\n\t\tLIMIT 100\n\t, [0 3 7 9 1 REPAYMENT WITHHOLD MANUAL_WITHHOLD]","duration":"40.9559ms","duration_ms":40}
{"level":"dev.info","ts":"[2025-08-14 09:50:00.048]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT DISTINCT\n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tbpt.transaction_no,\n\t\t\tbpt.channel_transaction_no,\n\t\t\tbpt.status as transaction_status\n\t\tFROM business_loan_orders blo\n\t\tINNER JOIN business_payment_transactions bpt ON blo.order_no = bpt.order_no\n\t\tWHERE blo.status = ?\n\t\t  AND bpt.type = ?\n\t\t  AND bpt.status = ?\n\t\t  AND bpt.channel_transaction_no != ''\n\t\t  AND bpt.channel_transaction_no IS NOT NULL\n\t\t  AND bpt.deleted_at IS NULL\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0 DISBURSEMENT 1]","duration":"45.7214ms","duration_ms":45}
{"level":"dev.info","ts":"[2025-08-14 09:50:00.685]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b7f134cfb585448a4158b","sql":"SELECT COUNT(DISTINCT user_id) as customer_count FROM `business_repayment_bills` WHERE DATE(due_date) < ? and `status` IN (?,?,?) LIMIT 1, [2025-08-14 3 9 2]","duration":"21.4939ms","duration_ms":21}
{"level":"dev.info","ts":"[2025-08-14 09:50:00.686]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b7f134cfb585448a4158b","sql":"SELECT COALESCE(SUM(total_due_amount - total_waive_amount), 0) as due_amount,COALESCE(SUM(due_principal), 0) as due_principal_amount FROM `business_repayment_bills` WHERE DATE(due_date) BETWEEN ? and ? LIMIT 1, [Y-m-d Y-m-d]","duration":"22.0118ms","duration_ms":22}
{"level":"dev.info","ts":"[2025-08-14 09:50:00.691]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b7f134cfb585448a4158b","sql":"SELECT COALESCE(SUM(loan_amount), 0) as total_amount, COUNT(*) as order_count, COALESCE(SUM(principal), 0) as principal_amount FROM `business_loan_orders` WHERE `status` IN (?,?) and DATE(disbursed_at) = ? LIMIT 1, [1 3 Y-m-d]","duration":"27.6834ms","duration_ms":27}
{"level":"dev.info","ts":"[2025-08-14 09:50:00.698]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b7f134cfb585448a4158b","sql":"SELECT COALESCE(SUM(total_due_amount - paid_amount - total_waive_amount), 0) as overdue_amount FROM `business_repayment_bills` WHERE DATE(due_date) < ? and `status` IN (?,?,?) LIMIT 1, [2025-08-14 3 9 2]","duration":"12.8827ms","duration_ms":12}
{"level":"dev.info","ts":"[2025-08-14 09:50:00.698]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b7f134cfb585448a4158b","sql":"SELECT COALESCE(SUM(paid_amount), 0) as repayment_amount FROM `business_repayment_bills` WHERE DATE(due_date) BETWEEN ? and ? LIMIT 1, [Y-m-d Y-m-d]","duration":"11.836ms","duration_ms":11}
{"level":"dev.info","ts":"[2025-08-14 09:50:00.703]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b7f134cfb585448a4158b","sql":"SELECT COUNT(DISTINCT user_id) as customer_count FROM `business_loan_orders` WHERE `status` IN (?,?) and DATE(disbursed_at) = ? LIMIT 1, [1 3 Y-m-d]","duration":"11.4468ms","duration_ms":11}
{"level":"dev.info","ts":"[2025-08-14 09:50:08.969]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b7f14e766113036105ba6","sql":"SELECT COUNT(DISTINCT user_id) as customer_count FROM `business_repayment_bills` WHERE DATE(due_date) < ? and `status` IN (?,?,?) LIMIT 1, [2025-08-14 3 9 2]","duration":"15.5735ms","duration_ms":15}
{"level":"dev.info","ts":"[2025-08-14 09:50:08.969]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b7f14e766113036105ba6","sql":"SELECT COALESCE(SUM(loan_amount), 0) as total_amount, COUNT(*) as order_count, COALESCE(SUM(principal), 0) as principal_amount FROM `business_loan_orders` WHERE `status` IN (?,?) and DATE(disbursed_at) = ? LIMIT 1, [1 3 Y-m-d]","duration":"16.1099ms","duration_ms":16}
{"level":"dev.info","ts":"[2025-08-14 09:50:08.969]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b7f14e766113036105ba6","sql":"SELECT COALESCE(SUM(total_due_amount - total_waive_amount), 0) as due_amount,COALESCE(SUM(due_principal), 0) as due_principal_amount FROM `business_repayment_bills` WHERE DATE(due_date) BETWEEN ? and ? LIMIT 1, [Y-m-d Y-m-d]","duration":"16.1099ms","duration_ms":16}
{"level":"dev.info","ts":"[2025-08-14 09:50:44.638]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b7f14e766113036105ba6","sql":"SELECT COALESCE(SUM(total_due_amount - paid_amount - total_waive_amount), 0) as overdue_amount FROM `business_repayment_bills` WHERE DATE(due_date) < ? and `status` IN (?,?,?) LIMIT 1, [2025-08-14 3 9 2]","duration":"35.6689678s","duration_ms":35668}
{"level":"dev.info","ts":"[2025-08-14 09:50:44.652]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b7f14e766113036105ba6","sql":"SELECT COALESCE(SUM(paid_amount), 0) as repayment_amount FROM `business_repayment_bills` WHERE DATE(due_date) BETWEEN ? and ? LIMIT 1, [Y-m-d Y-m-d]","duration":"31.2077ms","duration_ms":31}
{"level":"dev.info","ts":"[2025-08-14 09:50:44.652]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b7f14e766113036105ba6","sql":"SELECT COUNT(DISTINCT user_id) as customer_count FROM `business_loan_orders` WHERE `status` IN (?,?) and DATE(disbursed_at) = ? LIMIT 1, [1 3 Y-m-d]","duration":"31.7727ms","duration_ms":31}
{"level":"dev.info","ts":"[2025-08-14 09:51:00.041]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tid,\n\t\t\ttransaction_no,\n\t\t\torder_no,\n\t\t\tamount,\n\t\t\tcreated_at\n\t\tFROM business_payment_transactions \n\t\tWHERE type = ?\n\t\t  AND status = ?\n\t\t  AND deleted_at IS NULL\n\t\tORDER BY id ASC \n\t\tLIMIT 100\n\t, [REFUND 1]","duration":"41.1344ms","duration_ms":41}
{"level":"dev.info","ts":"[2025-08-14 09:51:00.042]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tc.channel_name,\n\t\t\tc.auto_label\n\t\tFROM business_loan_orders blo\n\t\tLEFT JOIN channel c ON blo.channel_id = c.id\n\t\tWHERE blo.status = ?\n\t\t  AND blo.risk_control_results = 0\n\t\t  AND c.auto_label = 0\n\t\t  AND c.channel_status = 1\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0]","duration":"41.8292ms","duration_ms":41}
{"level":"dev.info","ts":"[2025-08-14 09:52:00.081]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT DISTINCT\n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tbpt.transaction_no,\n\t\t\tbpt.channel_transaction_no,\n\t\t\tbpt.status as transaction_status\n\t\tFROM business_loan_orders blo\n\t\tINNER JOIN business_payment_transactions bpt ON blo.order_no = bpt.order_no\n\t\tWHERE blo.status = ?\n\t\t  AND bpt.type = ?\n\t\t  AND bpt.status = ?\n\t\t  AND bpt.channel_transaction_no != ''\n\t\t  AND bpt.channel_transaction_no IS NOT NULL\n\t\t  AND bpt.deleted_at IS NULL\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0 DISBURSEMENT 1]","duration":"80.9701ms","duration_ms":80}
{"level":"dev.info","ts":"[2025-08-14 09:52:00.098]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tc.channel_name,\n\t\t\tc.auto_label\n\t\tFROM business_loan_orders blo\n\t\tLEFT JOIN channel c ON blo.channel_id = c.id\n\t\tWHERE blo.status = ?\n\t\t  AND blo.risk_control_results = 0\n\t\t  AND c.auto_label = 0\n\t\t  AND c.channel_status = 1\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0]","duration":"97.2416ms","duration_ms":97}
{"level":"dev.info","ts":"[2025-08-14 09:52:00.106]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tid,\n\t\t\ttransaction_no,\n\t\t\torder_no,\n\t\t\tamount,\n\t\t\tcreated_at\n\t\tFROM business_payment_transactions \n\t\tWHERE type = ?\n\t\t  AND status = ?\n\t\t  AND deleted_at IS NULL\n\t\tORDER BY id ASC \n\t\tLIMIT 100\n\t, [REFUND 1]","duration":"105.0382ms","duration_ms":105}
{"level":"dev.info","ts":"[2025-08-14 09:52:00.106]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT DISTINCT\n\t\t\tbrb.id as bill_id,\n\t\t\tbrb.order_id,\n\t\t\tbrb.user_id,\n\t\t\tbrb.period_number,\n\t\t\tbrb.status as bill_status,\n\t\t\tbrb.total_due_amount,\n\t\t\tbrb.paid_amount,\n\t\t\tbpt.transaction_no,\n\t\t\tbpt.type as transaction_type,\n\t\t\tbpt.status as transaction_status,\n\t\t\tbpt.amount,\n\t\t\tbpt.third_party_order_no,\n\t\t\tbpt.created_at as transaction_created_at\n\t\tFROM business_repayment_bills brb\n\t\tINNER JOIN business_payment_transactions bpt ON brb.id = bpt.bill_id\n\t\tWHERE brb.status IN (?, ?, ?, ?)\n\t\t  AND bpt.status = ?\n\t\t  AND bpt.type IN (?, ?, ?)\n\t\t  AND bpt.transaction_no != ''\n\t\t  AND bpt.transaction_no IS NOT NULL\n\t\t  AND bpt.deleted_at IS NULL\n\t\tORDER BY bpt.created_at ASC\n\t\tLIMIT 100\n\t, [0 3 7 9 1 REPAYMENT WITHHOLD MANUAL_WITHHOLD]","duration":"106.01ms","duration_ms":106}
{"level":"dev.info","ts":"[2025-08-14 09:53:00.052]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tc.channel_name,\n\t\t\tc.auto_label\n\t\tFROM business_loan_orders blo\n\t\tLEFT JOIN channel c ON blo.channel_id = c.id\n\t\tWHERE blo.status = ?\n\t\t  AND blo.risk_control_results = 0\n\t\t  AND c.auto_label = 0\n\t\t  AND c.channel_status = 1\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0]","duration":"52.1ms","duration_ms":52}
{"level":"dev.info","ts":"[2025-08-14 09:53:00.054]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tid,\n\t\t\ttransaction_no,\n\t\t\torder_no,\n\t\t\tamount,\n\t\t\tcreated_at\n\t\tFROM business_payment_transactions \n\t\tWHERE type = ?\n\t\t  AND status = ?\n\t\t  AND deleted_at IS NULL\n\t\tORDER BY id ASC \n\t\tLIMIT 100\n\t, [REFUND 1]","duration":"53.853ms","duration_ms":53}
{"level":"dev.info","ts":"[2025-08-14 09:54:00.054]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tid,\n\t\t\ttransaction_no,\n\t\t\torder_no,\n\t\t\tamount,\n\t\t\tcreated_at\n\t\tFROM business_payment_transactions \n\t\tWHERE type = ?\n\t\t  AND status = ?\n\t\t  AND deleted_at IS NULL\n\t\tORDER BY id ASC \n\t\tLIMIT 100\n\t, [REFUND 1]","duration":"54.2266ms","duration_ms":54}
{"level":"dev.info","ts":"[2025-08-14 09:54:00.064]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT DISTINCT\n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tbpt.transaction_no,\n\t\t\tbpt.channel_transaction_no,\n\t\t\tbpt.status as transaction_status\n\t\tFROM business_loan_orders blo\n\t\tINNER JOIN business_payment_transactions bpt ON blo.order_no = bpt.order_no\n\t\tWHERE blo.status = ?\n\t\t  AND bpt.type = ?\n\t\t  AND bpt.status = ?\n\t\t  AND bpt.channel_transaction_no != ''\n\t\t  AND bpt.channel_transaction_no IS NOT NULL\n\t\t  AND bpt.deleted_at IS NULL\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0 DISBURSEMENT 1]","duration":"63.0952ms","duration_ms":63}
{"level":"dev.info","ts":"[2025-08-14 09:54:00.066]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT DISTINCT\n\t\t\tbrb.id as bill_id,\n\t\t\tbrb.order_id,\n\t\t\tbrb.user_id,\n\t\t\tbrb.period_number,\n\t\t\tbrb.status as bill_status,\n\t\t\tbrb.total_due_amount,\n\t\t\tbrb.paid_amount,\n\t\t\tbpt.transaction_no,\n\t\t\tbpt.type as transaction_type,\n\t\t\tbpt.status as transaction_status,\n\t\t\tbpt.amount,\n\t\t\tbpt.third_party_order_no,\n\t\t\tbpt.created_at as transaction_created_at\n\t\tFROM business_repayment_bills brb\n\t\tINNER JOIN business_payment_transactions bpt ON brb.id = bpt.bill_id\n\t\tWHERE brb.status IN (?, ?, ?, ?)\n\t\t  AND bpt.status = ?\n\t\t  AND bpt.type IN (?, ?, ?)\n\t\t  AND bpt.transaction_no != ''\n\t\t  AND bpt.transaction_no IS NOT NULL\n\t\t  AND bpt.deleted_at IS NULL\n\t\tORDER BY bpt.created_at ASC\n\t\tLIMIT 100\n\t, [0 3 7 9 1 REPAYMENT WITHHOLD MANUAL_WITHHOLD]","duration":"65.303ms","duration_ms":65}
{"level":"dev.info","ts":"[2025-08-14 09:54:00.066]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tc.channel_name,\n\t\t\tc.auto_label\n\t\tFROM business_loan_orders blo\n\t\tLEFT JOIN channel c ON blo.channel_id = c.id\n\t\tWHERE blo.status = ?\n\t\t  AND blo.risk_control_results = 0\n\t\t  AND c.auto_label = 0\n\t\t  AND c.channel_status = 1\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0]","duration":"65.303ms","duration_ms":65}
{"level":"dev.info","ts":"[2025-08-14 09:55:00.077]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tid,\n\t\t\ttransaction_no,\n\t\t\torder_no,\n\t\t\tamount,\n\t\t\tcreated_at\n\t\tFROM business_payment_transactions \n\t\tWHERE type = ?\n\t\t  AND status = ?\n\t\t  AND deleted_at IS NULL\n\t\tORDER BY id ASC \n\t\tLIMIT 100\n\t, [REFUND 1]","duration":"76.2922ms","duration_ms":76}
{"level":"dev.info","ts":"[2025-08-14 09:55:00.077]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tc.channel_name,\n\t\t\tc.auto_label\n\t\tFROM business_loan_orders blo\n\t\tLEFT JOIN channel c ON blo.channel_id = c.id\n\t\tWHERE blo.status = ?\n\t\t  AND blo.risk_control_results = 0\n\t\t  AND c.auto_label = 0\n\t\t  AND c.channel_status = 1\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0]","duration":"76.7995ms","duration_ms":76}
{"level":"dev.info","ts":"[2025-08-14 09:56:00.069]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tc.channel_name,\n\t\t\tc.auto_label\n\t\tFROM business_loan_orders blo\n\t\tLEFT JOIN channel c ON blo.channel_id = c.id\n\t\tWHERE blo.status = ?\n\t\t  AND blo.risk_control_results = 0\n\t\t  AND c.auto_label = 0\n\t\t  AND c.channel_status = 1\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0]","duration":"68.9594ms","duration_ms":68}
{"level":"dev.info","ts":"[2025-08-14 09:56:00.074]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tid,\n\t\t\ttransaction_no,\n\t\t\torder_no,\n\t\t\tamount,\n\t\t\tcreated_at\n\t\tFROM business_payment_transactions \n\t\tWHERE type = ?\n\t\t  AND status = ?\n\t\t  AND deleted_at IS NULL\n\t\tORDER BY id ASC \n\t\tLIMIT 100\n\t, [REFUND 1]","duration":"73.3481ms","duration_ms":73}
{"level":"dev.info","ts":"[2025-08-14 09:56:00.081]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT DISTINCT\n\t\t\tbrb.id as bill_id,\n\t\t\tbrb.order_id,\n\t\t\tbrb.user_id,\n\t\t\tbrb.period_number,\n\t\t\tbrb.status as bill_status,\n\t\t\tbrb.total_due_amount,\n\t\t\tbrb.paid_amount,\n\t\t\tbpt.transaction_no,\n\t\t\tbpt.type as transaction_type,\n\t\t\tbpt.status as transaction_status,\n\t\t\tbpt.amount,\n\t\t\tbpt.third_party_order_no,\n\t\t\tbpt.created_at as transaction_created_at\n\t\tFROM business_repayment_bills brb\n\t\tINNER JOIN business_payment_transactions bpt ON brb.id = bpt.bill_id\n\t\tWHERE brb.status IN (?, ?, ?, ?)\n\t\t  AND bpt.status = ?\n\t\t  AND bpt.type IN (?, ?, ?)\n\t\t  AND bpt.transaction_no != ''\n\t\t  AND bpt.transaction_no IS NOT NULL\n\t\t  AND bpt.deleted_at IS NULL\n\t\tORDER BY bpt.created_at ASC\n\t\tLIMIT 100\n\t, [0 3 7 9 1 REPAYMENT WITHHOLD MANUAL_WITHHOLD]","duration":"81.2061ms","duration_ms":81}
{"level":"dev.info","ts":"[2025-08-14 09:56:00.081]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT DISTINCT\n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tbpt.transaction_no,\n\t\t\tbpt.channel_transaction_no,\n\t\t\tbpt.status as transaction_status\n\t\tFROM business_loan_orders blo\n\t\tINNER JOIN business_payment_transactions bpt ON blo.order_no = bpt.order_no\n\t\tWHERE blo.status = ?\n\t\t  AND bpt.type = ?\n\t\t  AND bpt.status = ?\n\t\t  AND bpt.channel_transaction_no != ''\n\t\t  AND bpt.channel_transaction_no IS NOT NULL\n\t\t  AND bpt.deleted_at IS NULL\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0 DISBURSEMENT 1]","duration":"80.7004ms","duration_ms":80}
{"level":"dev.info","ts":"[2025-08-14 09:57:00.081]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tid,\n\t\t\ttransaction_no,\n\t\t\torder_no,\n\t\t\tamount,\n\t\t\tcreated_at\n\t\tFROM business_payment_transactions \n\t\tWHERE type = ?\n\t\t  AND status = ?\n\t\t  AND deleted_at IS NULL\n\t\tORDER BY id ASC \n\t\tLIMIT 100\n\t, [REFUND 1]","duration":"80.8642ms","duration_ms":80}
{"level":"dev.info","ts":"[2025-08-14 09:57:00.082]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tc.channel_name,\n\t\t\tc.auto_label\n\t\tFROM business_loan_orders blo\n\t\tLEFT JOIN channel c ON blo.channel_id = c.id\n\t\tWHERE blo.status = ?\n\t\t  AND blo.risk_control_results = 0\n\t\t  AND c.auto_label = 0\n\t\t  AND c.channel_status = 1\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0]","duration":"81.8799ms","duration_ms":81}
{"level":"dev.info","ts":"[2025-08-14 09:59:13.235]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b7f92610038b846cb2755","sql":"SELECT COALESCE(SUM(total_due_amount - total_waive_amount), 0) as due_amount,COALESCE(SUM(due_principal), 0) as due_principal_amount FROM `business_repayment_bills` WHERE DATE(due_date) BETWEEN ? and ? LIMIT 1, [Y-m-d Y-m-d]","duration":"12.5737ms","duration_ms":12}
{"level":"dev.info","ts":"[2025-08-14 09:59:13.280]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b7f92610038b846cb2755","sql":"SELECT COALESCE(SUM(paid_amount), 0) as repayment_amount FROM `business_repayment_bills` WHERE DATE(due_date) BETWEEN ? and ? LIMIT 1, [Y-m-d Y-m-d]","duration":"43.7931ms","duration_ms":43}
{"level":"dev.info","ts":"[2025-08-14 09:59:13.303]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b7f92610038b846cb2755","sql":"SELECT COUNT(DISTINCT user_id) as customer_count FROM `business_repayment_bills` WHERE DATE(due_date) < ? and `status` IN (?,?,?) LIMIT 1, [2025-08-14 3 9 2]","duration":"79.3995ms","duration_ms":79}
{"level":"dev.info","ts":"[2025-08-14 09:59:13.308]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b7f92610038b846cb2755","sql":"SELECT COALESCE(SUM(loan_amount), 0) as total_amount, COUNT(*) as order_count, COALESCE(SUM(principal), 0) as principal_amount FROM `business_loan_orders` WHERE `status` IN (?,?) and DATE(disbursed_at) = ? LIMIT 1, [1 3 2025-08-08]","duration":"85.1143ms","duration_ms":85}
{"level":"dev.info","ts":"[2025-08-14 09:59:18.095]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b7f92610038b846cb2755","sql":"SELECT COALESCE(SUM(total_due_amount - paid_amount - total_waive_amount), 0) as overdue_amount FROM `business_repayment_bills` WHERE DATE(due_date) < ? and `status` IN (?,?,?) LIMIT 1, [2025-08-14 3 9 2]","duration":"4.7920234s","duration_ms":4792}
{"level":"dev.info","ts":"[2025-08-14 09:59:18.124]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b7f92610038b846cb2755","sql":"SELECT COUNT(DISTINCT user_id) as customer_count FROM `business_loan_orders` WHERE `status` IN (?,?) and DATE(disbursed_at) = ? LIMIT 1, [1 3 2025-08-08]","duration":"28.9295ms","duration_ms":28}
{"level":"dev.info","ts":"[2025-08-14 09:59:57.901]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b7f9e5a0d721cec649607","sql":"SELECT COALESCE(SUM(total_due_amount - total_waive_amount), 0) as due_amount,COALESCE(SUM(due_principal), 0) as due_principal_amount FROM `business_repayment_bills` WHERE DATE(due_date) BETWEEN ? and ? LIMIT 1, [Y-m-d Y-m-d]","duration":"16.3481ms","duration_ms":16}
{"level":"dev.info","ts":"[2025-08-14 09:59:57.925]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b7f9e5a0d721cec649607","sql":"SELECT COALESCE(SUM(paid_amount), 0) as repayment_amount FROM `business_repayment_bills` WHERE DATE(due_date) BETWEEN ? and ? LIMIT 1, [Y-m-d Y-m-d]","duration":"23.6635ms","duration_ms":23}
{"level":"dev.info","ts":"[2025-08-14 09:59:57.950]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b7f9e5a0d721cec649607","sql":"SELECT COUNT(DISTINCT user_id) as customer_count FROM `business_repayment_bills` WHERE DATE(due_date) < ? and `status` IN (?,?,?) LIMIT 1, [2025-08-14 3 9 2]","duration":"65.5305ms","duration_ms":65}
{"level":"dev.info","ts":"[2025-08-14 09:59:57.950]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b7f9e5a0d721cec649607","sql":"SELECT COALESCE(SUM(loan_amount), 0) as total_amount, COUNT(*) as order_count, COALESCE(SUM(principal), 0) as principal_amount FROM `business_loan_orders` WHERE `status` IN (?,?) and DATE(disbursed_at) BETWEEN ? and ? LIMIT 1, [1 3 2025-08-08 2025-08-08]","duration":"65.5305ms","duration_ms":65}
{"level":"dev.info","ts":"[2025-08-14 09:59:57.978]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b7f9e5a0d721cec649607","sql":"SELECT COALESCE(SUM(total_due_amount - paid_amount - total_waive_amount), 0) as overdue_amount FROM `business_repayment_bills` WHERE DATE(due_date) < ? and `status` IN (?,?,?) LIMIT 1, [2025-08-14 3 9 2]","duration":"27.6646ms","duration_ms":27}
{"level":"dev.info","ts":"[2025-08-14 09:59:57.978]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b7f9e5a0d721cec649607","sql":"SELECT COUNT(DISTINCT user_id) as customer_count FROM `business_loan_orders` WHERE `status` IN (?,?) and DATE(disbursed_at) BETWEEN ? and ? LIMIT 1, [1 3 2025-08-08 2025-08-08]","duration":"27.6646ms","duration_ms":27}
{"level":"dev.info","ts":"[2025-08-14 09:59:59.293]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b7f9eab9bc5acd5e8481a","sql":"SELECT COALESCE(SUM(total_due_amount - total_waive_amount), 0) as due_amount,COALESCE(SUM(due_principal), 0) as due_principal_amount FROM `business_repayment_bills` WHERE DATE(due_date) BETWEEN ? and ? LIMIT 1, [Y-m-d Y-m-d]","duration":"41.481ms","duration_ms":41}
{"level":"dev.info","ts":"[2025-08-14 09:59:59.293]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b7f9eab9bc5acd5e8481a","sql":"SELECT COUNT(DISTINCT user_id) as customer_count FROM `business_repayment_bills` WHERE DATE(due_date) < ? and `status` IN (?,?,?) LIMIT 1, [2025-08-14 3 9 2]","duration":"40.2704ms","duration_ms":40}
{"level":"dev.info","ts":"[2025-08-14 09:59:59.293]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b7f9eab9bc5acd5e8481a","sql":"SELECT COALESCE(SUM(loan_amount), 0) as total_amount, COUNT(*) as order_count, COALESCE(SUM(principal), 0) as principal_amount FROM `business_loan_orders` WHERE `status` IN (?,?) and DATE(disbursed_at) BETWEEN ? and ? LIMIT 1, [1 3 2025-08-08 2025-08-08]","duration":"41.481ms","duration_ms":41}
{"level":"dev.info","ts":"[2025-08-14 09:59:59.308]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b7f9eab9bc5acd5e8481a","sql":"SELECT COALESCE(SUM(paid_amount), 0) as repayment_amount FROM `business_repayment_bills` WHERE DATE(due_date) BETWEEN ? and ? LIMIT 1, [Y-m-d Y-m-d]","duration":"15.8585ms","duration_ms":15}
{"level":"dev.info","ts":"[2025-08-14 09:59:59.308]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b7f9eab9bc5acd5e8481a","sql":"SELECT COALESCE(SUM(total_due_amount - paid_amount - total_waive_amount), 0) as overdue_amount FROM `business_repayment_bills` WHERE DATE(due_date) < ? and `status` IN (?,?,?) LIMIT 1, [2025-08-14 3 9 2]","duration":"15.8585ms","duration_ms":15}
{"level":"dev.info","ts":"[2025-08-14 09:59:59.310]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b7f9eab9bc5acd5e8481a","sql":"SELECT COUNT(DISTINCT user_id) as customer_count FROM `business_loan_orders` WHERE `status` IN (?,?) and DATE(disbursed_at) BETWEEN ? and ? LIMIT 1, [1 3 2025-08-08 2025-08-08]","duration":"16.8585ms","duration_ms":16}
{"level":"dev.info","ts":"[2025-08-14 10:00:00.017]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tid,\n\t\t\ttransaction_no,\n\t\t\torder_no,\n\t\t\tamount,\n\t\t\tcreated_at\n\t\tFROM business_payment_transactions \n\t\tWHERE type = ?\n\t\t  AND status = ?\n\t\t  AND deleted_at IS NULL\n\t\tORDER BY id ASC \n\t\tLIMIT 100\n\t, [REFUND 1]","duration":"16.6756ms","duration_ms":16}
{"level":"dev.info","ts":"[2025-08-14 10:00:00.018]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT DISTINCT\n\t\t\tbrb.id as bill_id,\n\t\t\tbrb.order_id,\n\t\t\tbrb.user_id,\n\t\t\tbrb.period_number,\n\t\t\tbrb.status as bill_status,\n\t\t\tbrb.total_due_amount,\n\t\t\tbrb.paid_amount,\n\t\t\tbpt.transaction_no,\n\t\t\tbpt.type as transaction_type,\n\t\t\tbpt.status as transaction_status,\n\t\t\tbpt.amount,\n\t\t\tbpt.third_party_order_no,\n\t\t\tbpt.created_at as transaction_created_at\n\t\tFROM business_repayment_bills brb\n\t\tINNER JOIN business_payment_transactions bpt ON brb.id = bpt.bill_id\n\t\tWHERE brb.status IN (?, ?, ?, ?)\n\t\t  AND bpt.status = ?\n\t\t  AND bpt.type IN (?, ?, ?)\n\t\t  AND bpt.transaction_no != ''\n\t\t  AND bpt.transaction_no IS NOT NULL\n\t\t  AND bpt.deleted_at IS NULL\n\t\tORDER BY bpt.created_at ASC\n\t\tLIMIT 100\n\t, [0 3 7 9 1 REPAYMENT WITHHOLD MANUAL_WITHHOLD]","duration":"17.1805ms","duration_ms":17}
{"level":"dev.info","ts":"[2025-08-14 10:00:00.018]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT DISTINCT\n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tbpt.transaction_no,\n\t\t\tbpt.channel_transaction_no,\n\t\t\tbpt.status as transaction_status\n\t\tFROM business_loan_orders blo\n\t\tINNER JOIN business_payment_transactions bpt ON blo.order_no = bpt.order_no\n\t\tWHERE blo.status = ?\n\t\t  AND bpt.type = ?\n\t\t  AND bpt.status = ?\n\t\t  AND bpt.channel_transaction_no != ''\n\t\t  AND bpt.channel_transaction_no IS NOT NULL\n\t\t  AND bpt.deleted_at IS NULL\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0 DISBURSEMENT 1]","duration":"15.5422ms","duration_ms":15}
{"level":"dev.info","ts":"[2025-08-14 10:00:00.046]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tc.channel_name,\n\t\t\tc.auto_label\n\t\tFROM business_loan_orders blo\n\t\tLEFT JOIN channel c ON blo.channel_id = c.id\n\t\tWHERE blo.status = ?\n\t\t  AND blo.risk_control_results = 0\n\t\t  AND c.auto_label = 0\n\t\t  AND c.channel_status = 1\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0]","duration":"43.5735ms","duration_ms":43}
{"level":"dev.info","ts":"[2025-08-14 10:00:00.046]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\t u.name AS username,\n\t\t\t u.mobile AS mobile,\n\t\t\t MIN(o.created_at) AS created_at \n\t\tFROM business_repayment_bills o\n\t\tJOIN business_app_account u ON o.user_id = u.id\n\t\tWHERE o.status = ? OR o.status = ? GROUP BY mobile\n\t, [9 3]","duration":"43.5735ms","duration_ms":43}
{"level":"dev.info","ts":"[2025-08-14 10:00:00.046]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `channel` WHERE `channel_status` = ?, [1]","duration":"43.5735ms","duration_ms":43}
{"level":"dev.info","ts":"[2025-08-14 10:00:00.060]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_app_account` WHERE `channelId` = ? and `createtime` >= ? and `createtime` < ? LIMIT 1, [3 ********** **********]","duration":"12.4913ms","duration_ms":12}
{"level":"dev.info","ts":"[2025-08-14 10:00:00.060]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_app_account` WHERE `channelId` = ? and `createtime` >= ? and `createtime` < ? LIMIT 1, [1 ********** **********]","duration":"12.4913ms","duration_ms":12}
{"level":"dev.info","ts":"[2025-08-14 10:00:00.060]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_app_account` WHERE `channelId` = ? and `createtime` >= ? and `createtime` < ? LIMIT 1, [2 ********** **********]","duration":"12.4913ms","duration_ms":12}
{"level":"dev.info","ts":"[2025-08-14 10:00:00.071]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_app_account` WHERE `channelId` = ? and `identityStatus` = ? and `identitySuccessTime` >= ? and `identitySuccessTime` < ? LIMIT 1, [1 2 2025-08-14 00:00:00 +0800 CST 2025-08-14 23:59:59.********* +0800 CST]","duration":"11.2822ms","duration_ms":11}
{"level":"dev.info","ts":"[2025-08-14 10:00:00.072]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_app_account` WHERE `channelId` = ? and `identityStatus` = ? and `identitySuccessTime` >= ? and `identitySuccessTime` < ? LIMIT 1, [2 2 2025-08-14 00:00:00 +0800 CST 2025-08-14 23:59:59.********* +0800 CST]","duration":"11.8044ms","duration_ms":11}
{"level":"dev.info","ts":"[2025-08-14 10:00:00.072]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_app_account` WHERE `channelId` = ? and `identityStatus` = ? and `identitySuccessTime` >= ? and `identitySuccessTime` < ? LIMIT 1, [3 2 2025-08-14 00:00:00 +0800 CST 2025-08-14 23:59:59.********* +0800 CST]","duration":"12.3215ms","duration_ms":12}
{"level":"dev.info","ts":"[2025-08-14 10:00:00.093]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_loan_orders` WHERE `channel_id` = ? and `status` IN (?,?) and `disbursed_at` >= ? and `disbursed_at` < ? LIMIT 1, [2 1 3 2025-08-14 00:00:00 +0800 CST 2025-08-14 23:59:59.********* +0800 CST]","duration":"21.2575ms","duration_ms":21}
{"level":"dev.info","ts":"[2025-08-14 10:00:00.093]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_loan_orders` WHERE `channel_id` = ? and `status` IN (?,?) and `disbursed_at` >= ? and `disbursed_at` < ? LIMIT 1, [1 1 3 2025-08-14 00:00:00 +0800 CST 2025-08-14 23:59:59.********* +0800 CST]","duration":"21.2575ms","duration_ms":21}
{"level":"dev.info","ts":"[2025-08-14 10:00:00.093]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `business_loan_orders` WHERE `channel_id` = ? and `status` IN (?,?) and `disbursed_at` >= ? and `disbursed_at` < ? LIMIT 1, [3 1 3 2025-08-14 00:00:00 +0800 CST 2025-08-14 23:59:59.********* +0800 CST]","duration":"21.2575ms","duration_ms":21}
{"level":"dev.info","ts":"[2025-08-14 10:00:00.106]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `channel_statistics` WHERE `channel_id` = ? and DATE(created_at) = ? LIMIT 1, [3 2025-08-14]","duration":"10.9463ms","duration_ms":10}
{"level":"dev.info","ts":"[2025-08-14 10:00:00.106]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `channel_statistics` WHERE `channel_id` = ? and DATE(created_at) = ? LIMIT 1, [2 2025-08-14]","duration":"12.3744ms","duration_ms":12}
{"level":"dev.info","ts":"[2025-08-14 10:00:00.106]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `channel_statistics` WHERE `channel_id` = ? and DATE(created_at) = ? LIMIT 1, [1 2025-08-14]","duration":"12.879ms","duration_ms":12}
{"level":"dev.info","ts":"[2025-08-14 10:00:00.124]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"UPDATE `channel_statistics` SET `real_name_num` = ?,`number_of_transactions` = ?,`updated_at` = ?,`new_customer_reg_num` = ? WHERE `channel_id` = ? and DATE(created_at) = ?, [0 0 2025-08-14 10:00:00.1060756 +0800 CST m=+5.602832001 0 2 2025-08-14]","duration":"17.1267ms","duration_ms":17}
{"level":"dev.info","ts":"[2025-08-14 10:00:00.131]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"UPDATE `channel_statistics` SET `updated_at` = ?,`new_customer_reg_num` = ?,`real_name_num` = ?,`number_of_transactions` = ? WHERE `channel_id` = ? and DATE(created_at) = ?, [2025-08-14 10:00:00.1060756 +0800 CST m=+5.602832001 0 0 0 3 2025-08-14]","duration":"25.4141ms","duration_ms":25}
{"level":"dev.info","ts":"[2025-08-14 10:00:00.132]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"UPDATE `channel_statistics` SET `number_of_transactions` = ?,`updated_at` = ?,`new_customer_reg_num` = ?,`real_name_num` = ? WHERE `channel_id` = ? and DATE(created_at) = ?, [0 2025-08-14 10:00:00.1065802 +0800 CST m=+5.603336601 0 0 1 2025-08-14]","duration":"25.5359ms","duration_ms":25}
{"level":"dev.info","ts":"[2025-08-14 10:00:03.958]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b7f9fc2cdcb48d1ce3318","sql":"SELECT COALESCE(SUM(total_due_amount - total_waive_amount), 0) as due_amount,COALESCE(SUM(due_principal), 0) as due_principal_amount FROM `business_repayment_bills` WHERE DATE(due_date) BETWEEN ? and ? LIMIT 1, [Y-m-d Y-m-d]","duration":"22.8966ms","duration_ms":22}
{"level":"dev.info","ts":"[2025-08-14 10:00:03.958]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b7f9fc2cdcb48d1ce3318","sql":"SELECT COUNT(DISTINCT user_id) as customer_count FROM `business_repayment_bills` WHERE DATE(due_date) < ? and `status` IN (?,?,?) LIMIT 1, [2025-08-14 3 9 2]","duration":"22.3856ms","duration_ms":22}
{"level":"dev.info","ts":"[2025-08-14 10:00:03.958]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b7f9fc2cdcb48d1ce3318","sql":"SELECT COALESCE(SUM(loan_amount), 0) as total_amount, COUNT(*) as order_count, COALESCE(SUM(principal), 0) as principal_amount FROM `business_loan_orders` WHERE `status` IN (?,?) and DATE(disbursed_at) BETWEEN ? and ? LIMIT 1, [1 3 2025-08-01 2025-08-08]","duration":"22.8966ms","duration_ms":22}
{"level":"dev.info","ts":"[2025-08-14 10:00:03.969]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b7f9fc2cdcb48d1ce3318","sql":"SELECT COALESCE(SUM(total_due_amount - paid_amount - total_waive_amount), 0) as overdue_amount FROM `business_repayment_bills` WHERE DATE(due_date) < ? and `status` IN (?,?,?) LIMIT 1, [2025-08-14 3 9 2]","duration":"11.2914ms","duration_ms":11}
{"level":"dev.info","ts":"[2025-08-14 10:00:03.970]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b7f9fc2cdcb48d1ce3318","sql":"SELECT COUNT(DISTINCT user_id) as customer_count FROM `business_loan_orders` WHERE `status` IN (?,?) and DATE(disbursed_at) BETWEEN ? and ? LIMIT 1, [1 3 2025-08-01 2025-08-08]","duration":"11.8346ms","duration_ms":11}
{"level":"dev.info","ts":"[2025-08-14 10:00:03.970]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b7f9fc2cdcb48d1ce3318","sql":"SELECT COALESCE(SUM(paid_amount), 0) as repayment_amount FROM `business_repayment_bills` WHERE DATE(due_date) BETWEEN ? and ? LIMIT 1, [Y-m-d Y-m-d]","duration":"11.8346ms","duration_ms":11}
{"level":"dev.info","ts":"[2025-08-14 10:00:05.158]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b7fa009e258788e2cf1a0","sql":"SELECT COALESCE(SUM(total_due_amount - total_waive_amount), 0) as due_amount,COALESCE(SUM(due_principal), 0) as due_principal_amount FROM `business_repayment_bills` WHERE DATE(due_date) BETWEEN ? and ? LIMIT 1, [Y-m-d Y-m-d]","duration":"29.6189ms","duration_ms":29}
{"level":"dev.info","ts":"[2025-08-14 10:00:05.158]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b7fa009e258788e2cf1a0","sql":"SELECT COALESCE(SUM(loan_amount), 0) as total_amount, COUNT(*) as order_count, COALESCE(SUM(principal), 0) as principal_amount FROM `business_loan_orders` WHERE `status` IN (?,?) and DATE(disbursed_at) BETWEEN ? and ? LIMIT 1, [1 3 2025-08-01 2025-08-08]","duration":"29.6189ms","duration_ms":29}
{"level":"dev.info","ts":"[2025-08-14 10:00:05.158]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b7fa009e258788e2cf1a0","sql":"SELECT COUNT(DISTINCT user_id) as customer_count FROM `business_repayment_bills` WHERE DATE(due_date) < ? and `status` IN (?,?,?) LIMIT 1, [2025-08-14 3 9 2]","duration":"29.5536ms","duration_ms":29}
{"level":"dev.info","ts":"[2025-08-14 10:00:05.174]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b7fa009e258788e2cf1a0","sql":"SELECT COUNT(DISTINCT user_id) as customer_count FROM `business_loan_orders` WHERE `status` IN (?,?) and DATE(disbursed_at) BETWEEN ? and ? LIMIT 1, [1 3 2025-08-01 2025-08-08]","duration":"15.2287ms","duration_ms":15}
{"level":"dev.info","ts":"[2025-08-14 10:00:05.184]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b7fa009e258788e2cf1a0","sql":"SELECT COALESCE(SUM(paid_amount), 0) as repayment_amount FROM `business_repayment_bills` WHERE DATE(due_date) BETWEEN ? and ? LIMIT 1, [Y-m-d Y-m-d]","duration":"25.9935ms","duration_ms":25}
{"level":"dev.info","ts":"[2025-08-14 10:00:05.184]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b7fa009e258788e2cf1a0","sql":"SELECT COALESCE(SUM(total_due_amount - paid_amount - total_waive_amount), 0) as overdue_amount FROM `business_repayment_bills` WHERE DATE(due_date) < ? and `status` IN (?,?,?) LIMIT 1, [2025-08-14 3 9 2]","duration":"25.4879ms","duration_ms":25}
{"level":"dev.info","ts":"[2025-08-14 10:00:10.531]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b7fa14b234e68664b2b71","sql":"SELECT COUNT(DISTINCT user_id) as customer_count FROM `business_repayment_bills` WHERE DATE(due_date) < ? and `status` IN (?,?,?) LIMIT 1, [2025-08-14 3 9 2]","duration":"12.4984ms","duration_ms":12}
{"level":"dev.info","ts":"[2025-08-14 10:00:10.531]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b7fa14b234e68664b2b71","sql":"SELECT COALESCE(SUM(total_due_amount - total_waive_amount), 0) as due_amount,COALESCE(SUM(due_principal), 0) as due_principal_amount FROM `business_repayment_bills` WHERE DATE(due_date) BETWEEN ? and ? LIMIT 1, [Y-m-d Y-m-d]","duration":"13.0094ms","duration_ms":13}
{"level":"dev.info","ts":"[2025-08-14 10:00:10.538]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b7fa14b234e68664b2b71","sql":"SELECT COALESCE(SUM(loan_amount), 0) as total_amount, COUNT(*) as order_count, COALESCE(SUM(principal), 0) as principal_amount FROM `business_loan_orders` WHERE `status` IN (?,?) and DATE(disbursed_at) BETWEEN ? and ? LIMIT 1, [1 3 2025-08-01 2025-08-12]","duration":"20.5492ms","duration_ms":20}
{"level":"dev.info","ts":"[2025-08-14 10:00:10.546]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b7fa14b234e68664b2b71","sql":"SELECT COALESCE(SUM(total_due_amount - paid_amount - total_waive_amount), 0) as overdue_amount FROM `business_repayment_bills` WHERE DATE(due_date) < ? and `status` IN (?,?,?) LIMIT 1, [2025-08-14 3 9 2]","duration":"15.1138ms","duration_ms":15}
{"level":"dev.info","ts":"[2025-08-14 10:00:10.546]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b7fa14b234e68664b2b71","sql":"SELECT COALESCE(SUM(paid_amount), 0) as repayment_amount FROM `business_repayment_bills` WHERE DATE(due_date) BETWEEN ? and ? LIMIT 1, [Y-m-d Y-m-d]","duration":"15.1138ms","duration_ms":15}
{"level":"dev.info","ts":"[2025-08-14 10:00:10.572]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b7fa14b234e68664b2b71","sql":"SELECT COUNT(DISTINCT user_id) as customer_count FROM `business_loan_orders` WHERE `status` IN (?,?) and DATE(disbursed_at) BETWEEN ? and ? LIMIT 1, [1 3 2025-08-01 2025-08-12]","duration":"33.6819ms","duration_ms":33}
{"level":"dev.info","ts":"[2025-08-14 10:10:56.244]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b8037a2c581e0622e1545","sql":"SELECT COALESCE(SUM(total_due_amount - total_waive_amount), 0) as due_amount,COALESCE(SUM(due_principal), 0) as due_principal_amount FROM `business_repayment_bills` WHERE DATE(due_date) BETWEEN ? and ? LIMIT 1, [2025-08-08 2025-08-08]","duration":"9.2284ms","duration_ms":9}
{"level":"dev.info","ts":"[2025-08-14 10:10:56.254]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b8037a2c581e0622e1545","sql":"SELECT COALESCE(SUM(paid_amount), 0) as repayment_amount FROM `business_repayment_bills` WHERE DATE(due_date) BETWEEN ? and ? LIMIT 1, [2025-08-08 2025-08-08]","duration":"10.0019ms","duration_ms":10}
{"level":"dev.info","ts":"[2025-08-14 10:10:56.287]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b8037a2c581e0622e1545","sql":"SELECT COALESCE(SUM(loan_amount), 0) as total_amount, COUNT(*) as order_count, COALESCE(SUM(principal), 0) as principal_amount FROM `business_loan_orders` WHERE `status` IN (?,?) and DATE(disbursed_at) BETWEEN ? and ? LIMIT 1, [1 3 2025-08-08 2025-08-08]","duration":"52.3048ms","duration_ms":52}
{"level":"dev.info","ts":"[2025-08-14 10:10:56.287]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b8037a2c581e0622e1545","sql":"SELECT COUNT(DISTINCT user_id) as customer_count FROM `business_repayment_bills` WHERE DATE(due_date) < ? and `status` IN (?,?,?) LIMIT 1, [2025-08-14 3 9 2]","duration":"52.3048ms","duration_ms":52}
{"level":"dev.info","ts":"[2025-08-14 10:10:56.300]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b8037a2c581e0622e1545","sql":"SELECT COALESCE(SUM(total_due_amount - paid_amount - total_waive_amount), 0) as overdue_amount FROM `business_repayment_bills` WHERE DATE(due_date) < ? and `status` IN (?,?,?) LIMIT 1, [2025-08-14 3 9 2]","duration":"12.799ms","duration_ms":12}
{"level":"dev.info","ts":"[2025-08-14 10:10:56.300]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b8037a2c581e0622e1545","sql":"SELECT COUNT(DISTINCT user_id) as customer_count FROM `business_loan_orders` WHERE `status` IN (?,?) and DATE(disbursed_at) BETWEEN ? and ? LIMIT 1, [1 3 2025-08-08 2025-08-08]","duration":"13.3058ms","duration_ms":13}
{"level":"dev.info","ts":"[2025-08-14 10:11:00.025]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tid,\n\t\t\ttransaction_no,\n\t\t\torder_no,\n\t\t\tamount,\n\t\t\tcreated_at\n\t\tFROM business_payment_transactions \n\t\tWHERE type = ?\n\t\t  AND status = ?\n\t\t  AND deleted_at IS NULL\n\t\tORDER BY id ASC \n\t\tLIMIT 100\n\t, [REFUND 1]","duration":"23.6895ms","duration_ms":23}
{"level":"dev.info","ts":"[2025-08-14 10:11:00.025]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tc.channel_name,\n\t\t\tc.auto_label\n\t\tFROM business_loan_orders blo\n\t\tLEFT JOIN channel c ON blo.channel_id = c.id\n\t\tWHERE blo.status = ?\n\t\t  AND blo.risk_control_results = 0\n\t\t  AND c.auto_label = 0\n\t\t  AND c.channel_status = 1\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0]","duration":"23.6895ms","duration_ms":23}
{"level":"dev.info","ts":"[2025-08-14 10:11:09.156]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b803aa4739d58833a5a8b","sql":"SELECT COALESCE(SUM(total_due_amount - total_waive_amount), 0) as due_amount,COALESCE(SUM(due_principal), 0) as due_principal_amount FROM `business_repayment_bills` WHERE DATE(due_date) BETWEEN ? and ? LIMIT 1, [2025-08-14 2025-08-14]","duration":"10.1305ms","duration_ms":10}
{"level":"dev.info","ts":"[2025-08-14 10:11:09.156]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b803aa4739d58833a5a8b","sql":"SELECT COALESCE(SUM(loan_amount), 0) as total_amount, COUNT(*) as order_count, COALESCE(SUM(principal), 0) as principal_amount FROM `business_loan_orders` WHERE `status` IN (?,?) and DATE(disbursed_at) BETWEEN ? and ? LIMIT 1, [1 3 2025-08-14 2025-08-14]","duration":"10.1305ms","duration_ms":10}
{"level":"dev.info","ts":"[2025-08-14 10:11:09.156]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b803aa4739d58833a5a8b","sql":"SELECT COUNT(DISTINCT user_id) as customer_count FROM `business_repayment_bills` WHERE DATE(due_date) < ? and `status` IN (?,?,?) LIMIT 1, [2025-08-14 3 9 2]","duration":"9.0195ms","duration_ms":9}
{"level":"dev.info","ts":"[2025-08-14 10:11:09.172]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b803aa4739d58833a5a8b","sql":"SELECT COALESCE(SUM(paid_amount), 0) as repayment_amount FROM `business_repayment_bills` WHERE DATE(due_date) BETWEEN ? and ? LIMIT 1, [2025-08-14 2025-08-14]","duration":"15.7556ms","duration_ms":15}
{"level":"dev.info","ts":"[2025-08-14 10:11:09.172]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b803aa4739d58833a5a8b","sql":"SELECT COALESCE(SUM(total_due_amount - paid_amount - total_waive_amount), 0) as overdue_amount FROM `business_repayment_bills` WHERE DATE(due_date) < ? and `status` IN (?,?,?) LIMIT 1, [2025-08-14 3 9 2]","duration":"14.751ms","duration_ms":14}
{"level":"dev.info","ts":"[2025-08-14 10:11:09.172]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b803aa4739d58833a5a8b","sql":"SELECT COUNT(DISTINCT user_id) as customer_count FROM `business_loan_orders` WHERE `status` IN (?,?) and DATE(disbursed_at) BETWEEN ? and ? LIMIT 1, [1 3 2025-08-14 2025-08-14]","duration":"15.7556ms","duration_ms":15}
{"level":"dev.info","ts":"[2025-08-14 10:11:13.429]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b803ba2dc874caedb1b56","sql":"SELECT COALESCE(SUM(total_due_amount - total_waive_amount), 0) as due_amount,COALESCE(SUM(due_principal), 0) as due_principal_amount FROM `business_repayment_bills` WHERE DATE(due_date) BETWEEN ? and ? LIMIT 1, [2025-08-08 2025-08-14]","duration":"14.4804ms","duration_ms":14}
{"level":"dev.info","ts":"[2025-08-14 10:11:13.436]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b803ba2dc874caedb1b56","sql":"SELECT COUNT(DISTINCT user_id) as customer_count FROM `business_repayment_bills` WHERE DATE(due_date) < ? and `status` IN (?,?,?) LIMIT 1, [2025-08-14 3 9 2]","duration":"20.9129ms","duration_ms":20}
{"level":"dev.info","ts":"[2025-08-14 10:11:13.436]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b803ba2dc874caedb1b56","sql":"SELECT COALESCE(SUM(loan_amount), 0) as total_amount, COUNT(*) as order_count, COALESCE(SUM(principal), 0) as principal_amount FROM `business_loan_orders` WHERE `status` IN (?,?) and DATE(disbursed_at) BETWEEN ? and ? LIMIT 1, [1 3 2025-08-08 2025-08-14]","duration":"21.4982ms","duration_ms":21}
{"level":"dev.info","ts":"[2025-08-14 10:11:13.440]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b803ba2dc874caedb1b56","sql":"SELECT COALESCE(SUM(paid_amount), 0) as repayment_amount FROM `business_repayment_bills` WHERE DATE(due_date) BETWEEN ? and ? LIMIT 1, [2025-08-08 2025-08-14]","duration":"10.8983ms","duration_ms":10}
{"level":"dev.info","ts":"[2025-08-14 10:11:13.446]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b803ba2dc874caedb1b56","sql":"SELECT COALESCE(SUM(total_due_amount - paid_amount - total_waive_amount), 0) as overdue_amount FROM `business_repayment_bills` WHERE DATE(due_date) < ? and `status` IN (?,?,?) LIMIT 1, [2025-08-14 3 9 2]","duration":"9.9454ms","duration_ms":9}
{"level":"dev.info","ts":"[2025-08-14 10:11:13.451]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b803ba2dc874caedb1b56","sql":"SELECT COUNT(DISTINCT user_id) as customer_count FROM `business_loan_orders` WHERE `status` IN (?,?) and DATE(disbursed_at) BETWEEN ? and ? LIMIT 1, [1 3 2025-08-08 2025-08-14]","duration":"14.9239ms","duration_ms":14}
{"level":"dev.info","ts":"[2025-08-14 10:11:18.266]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b803cc21628108e386f33","sql":"SELECT COALESCE(SUM(total_due_amount - total_waive_amount), 0) as due_amount,COALESCE(SUM(due_principal), 0) as due_principal_amount FROM `business_repayment_bills` WHERE DATE(due_date) BETWEEN ? and ? LIMIT 1, [2025-08-09 2025-08-14]","duration":"32.7623ms","duration_ms":32}
{"level":"dev.info","ts":"[2025-08-14 10:11:18.266]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b803cc21628108e386f33","sql":"SELECT COUNT(DISTINCT user_id) as customer_count FROM `business_repayment_bills` WHERE DATE(due_date) < ? and `status` IN (?,?,?) LIMIT 1, [2025-08-14 3 9 2]","duration":"32.257ms","duration_ms":32}
{"level":"dev.info","ts":"[2025-08-14 10:11:18.266]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b803cc21628108e386f33","sql":"SELECT COALESCE(SUM(loan_amount), 0) as total_amount, COUNT(*) as order_count, COALESCE(SUM(principal), 0) as principal_amount FROM `business_loan_orders` WHERE `status` IN (?,?) and DATE(disbursed_at) BETWEEN ? and ? LIMIT 1, [1 3 2025-08-09 2025-08-14]","duration":"32.7623ms","duration_ms":32}
{"level":"dev.info","ts":"[2025-08-14 10:11:18.275]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b803cc21628108e386f33","sql":"SELECT COUNT(DISTINCT user_id) as customer_count FROM `business_loan_orders` WHERE `status` IN (?,?) and DATE(disbursed_at) BETWEEN ? and ? LIMIT 1, [1 3 2025-08-09 2025-08-14]","duration":"9.4441ms","duration_ms":9}
{"level":"dev.info","ts":"[2025-08-14 10:11:18.279]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b803cc21628108e386f33","sql":"SELECT COALESCE(SUM(paid_amount), 0) as repayment_amount FROM `business_repayment_bills` WHERE DATE(due_date) BETWEEN ? and ? LIMIT 1, [2025-08-09 2025-08-14]","duration":"13.2987ms","duration_ms":13}
{"level":"dev.info","ts":"[2025-08-14 10:11:18.279]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b803cc21628108e386f33","sql":"SELECT COALESCE(SUM(total_due_amount - paid_amount - total_waive_amount), 0) as overdue_amount FROM `business_repayment_bills` WHERE DATE(due_date) < ? and `status` IN (?,?,?) LIMIT 1, [2025-08-14 3 9 2]","duration":"13.2987ms","duration_ms":13}
{"level":"dev.info","ts":"[2025-08-14 10:12:00.053]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tid,\n\t\t\ttransaction_no,\n\t\t\torder_no,\n\t\t\tamount,\n\t\t\tcreated_at\n\t\tFROM business_payment_transactions \n\t\tWHERE type = ?\n\t\t  AND status = ?\n\t\t  AND deleted_at IS NULL\n\t\tORDER BY id ASC \n\t\tLIMIT 100\n\t, [REFUND 1]","duration":"53.1098ms","duration_ms":53}
{"level":"dev.info","ts":"[2025-08-14 10:12:00.053]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT DISTINCT\n\t\t\tbrb.id as bill_id,\n\t\t\tbrb.order_id,\n\t\t\tbrb.user_id,\n\t\t\tbrb.period_number,\n\t\t\tbrb.status as bill_status,\n\t\t\tbrb.total_due_amount,\n\t\t\tbrb.paid_amount,\n\t\t\tbpt.transaction_no,\n\t\t\tbpt.type as transaction_type,\n\t\t\tbpt.status as transaction_status,\n\t\t\tbpt.amount,\n\t\t\tbpt.third_party_order_no,\n\t\t\tbpt.created_at as transaction_created_at\n\t\tFROM business_repayment_bills brb\n\t\tINNER JOIN business_payment_transactions bpt ON brb.id = bpt.bill_id\n\t\tWHERE brb.status IN (?, ?, ?, ?)\n\t\t  AND bpt.status = ?\n\t\t  AND bpt.type IN (?, ?, ?)\n\t\t  AND bpt.transaction_no != ''\n\t\t  AND bpt.transaction_no IS NOT NULL\n\t\t  AND bpt.deleted_at IS NULL\n\t\tORDER BY bpt.created_at ASC\n\t\tLIMIT 100\n\t, [0 3 7 9 1 REPAYMENT WITHHOLD MANUAL_WITHHOLD]","duration":"52.5976ms","duration_ms":52}
{"level":"dev.info","ts":"[2025-08-14 10:12:00.053]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tc.channel_name,\n\t\t\tc.auto_label\n\t\tFROM business_loan_orders blo\n\t\tLEFT JOIN channel c ON blo.channel_id = c.id\n\t\tWHERE blo.status = ?\n\t\t  AND blo.risk_control_results = 0\n\t\t  AND c.auto_label = 0\n\t\t  AND c.channel_status = 1\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0]","duration":"53.4872ms","duration_ms":53}
{"level":"dev.info","ts":"[2025-08-14 10:12:00.053]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT DISTINCT\n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tbpt.transaction_no,\n\t\t\tbpt.channel_transaction_no,\n\t\t\tbpt.status as transaction_status\n\t\tFROM business_loan_orders blo\n\t\tINNER JOIN business_payment_transactions bpt ON blo.order_no = bpt.order_no\n\t\tWHERE blo.status = ?\n\t\t  AND bpt.type = ?\n\t\t  AND bpt.status = ?\n\t\t  AND bpt.channel_transaction_no != ''\n\t\t  AND bpt.channel_transaction_no IS NOT NULL\n\t\t  AND bpt.deleted_at IS NULL\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0 DISBURSEMENT 1]","duration":"52.6644ms","duration_ms":52}
{"level":"dev.info","ts":"[2025-08-14 10:13:00.048]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tc.channel_name,\n\t\t\tc.auto_label\n\t\tFROM business_loan_orders blo\n\t\tLEFT JOIN channel c ON blo.channel_id = c.id\n\t\tWHERE blo.status = ?\n\t\t  AND blo.risk_control_results = 0\n\t\t  AND c.auto_label = 0\n\t\t  AND c.channel_status = 1\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0]","duration":"48.1792ms","duration_ms":48}
{"level":"dev.info","ts":"[2025-08-14 10:13:00.052]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tid,\n\t\t\ttransaction_no,\n\t\t\torder_no,\n\t\t\tamount,\n\t\t\tcreated_at\n\t\tFROM business_payment_transactions \n\t\tWHERE type = ?\n\t\t  AND status = ?\n\t\t  AND deleted_at IS NULL\n\t\tORDER BY id ASC \n\t\tLIMIT 100\n\t, [REFUND 1]","duration":"51.8929ms","duration_ms":51}
{"level":"dev.info","ts":"[2025-08-14 10:14:00.149]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tid,\n\t\t\ttransaction_no,\n\t\t\torder_no,\n\t\t\tamount,\n\t\t\tcreated_at\n\t\tFROM business_payment_transactions \n\t\tWHERE type = ?\n\t\t  AND status = ?\n\t\t  AND deleted_at IS NULL\n\t\tORDER BY id ASC \n\t\tLIMIT 100\n\t, [REFUND 1]","duration":"149.1443ms","duration_ms":149}
{"level":"dev.info","ts":"[2025-08-14 10:14:00.150]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT DISTINCT\n\t\t\tbrb.id as bill_id,\n\t\t\tbrb.order_id,\n\t\t\tbrb.user_id,\n\t\t\tbrb.period_number,\n\t\t\tbrb.status as bill_status,\n\t\t\tbrb.total_due_amount,\n\t\t\tbrb.paid_amount,\n\t\t\tbpt.transaction_no,\n\t\t\tbpt.type as transaction_type,\n\t\t\tbpt.status as transaction_status,\n\t\t\tbpt.amount,\n\t\t\tbpt.third_party_order_no,\n\t\t\tbpt.created_at as transaction_created_at\n\t\tFROM business_repayment_bills brb\n\t\tINNER JOIN business_payment_transactions bpt ON brb.id = bpt.bill_id\n\t\tWHERE brb.status IN (?, ?, ?, ?)\n\t\t  AND bpt.status = ?\n\t\t  AND bpt.type IN (?, ?, ?)\n\t\t  AND bpt.transaction_no != ''\n\t\t  AND bpt.transaction_no IS NOT NULL\n\t\t  AND bpt.deleted_at IS NULL\n\t\tORDER BY bpt.created_at ASC\n\t\tLIMIT 100\n\t, [0 3 7 9 1 REPAYMENT WITHHOLD MANUAL_WITHHOLD]","duration":"150.1586ms","duration_ms":150}
{"level":"dev.info","ts":"[2025-08-14 10:14:00.157]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tc.channel_name,\n\t\t\tc.auto_label\n\t\tFROM business_loan_orders blo\n\t\tLEFT JOIN channel c ON blo.channel_id = c.id\n\t\tWHERE blo.status = ?\n\t\t  AND blo.risk_control_results = 0\n\t\t  AND c.auto_label = 0\n\t\t  AND c.channel_status = 1\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0]","duration":"156.1195ms","duration_ms":156}
{"level":"dev.info","ts":"[2025-08-14 10:14:00.157]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT DISTINCT\n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tbpt.transaction_no,\n\t\t\tbpt.channel_transaction_no,\n\t\t\tbpt.status as transaction_status\n\t\tFROM business_loan_orders blo\n\t\tINNER JOIN business_payment_transactions bpt ON blo.order_no = bpt.order_no\n\t\tWHERE blo.status = ?\n\t\t  AND bpt.type = ?\n\t\t  AND bpt.status = ?\n\t\t  AND bpt.channel_transaction_no != ''\n\t\t  AND bpt.channel_transaction_no IS NOT NULL\n\t\t  AND bpt.deleted_at IS NULL\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0 DISBURSEMENT 1]","duration":"156.7083ms","duration_ms":156}
