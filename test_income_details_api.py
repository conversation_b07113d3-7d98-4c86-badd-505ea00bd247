#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
收入明细接口测试脚本
"""

import requests
import json
import sys

def test_income_details_api():
    """测试收入明细接口"""
    
    # 接口URL
    base_url = "http://localhost:8108"
    endpoint = "/business/statistics/statisticscontroller/getIncomeDetails"
    url = f"{base_url}{endpoint}"
    
    # 测试参数
    params = {
        "page": 1,
        "page_size": 10,
        # "fund_type": "收款",
        # "payment_method": "资管支付",
        # "payment_status": "到期收款",
        # "payment_time_start": "2024-01-01",
        # "payment_time_end": "2024-12-31"
    }
    
    try:
        print(f"正在测试接口: {url}")
        print(f"请求参数: {json.dumps(params, indent=2, ensure_ascii=False)}")
        
        # 发送GET请求
        response = requests.get(url, params=params, timeout=30)
        
        print(f"响应状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        
        if response.status_code == 200:
            try:
                result = response.json()
                print("响应内容:")
                print(json.dumps(result, indent=2, ensure_ascii=False))
                
                # 检查响应结构
                if "code" in result:
                    if result["code"] == 200:
                        print("✅ 接口调用成功!")
                        
                        # 检查数据结构
                        if "data" in result:
                            data = result["data"]
                            if "statistics" in data and "list" in data:
                                print("✅ 响应数据结构正确!")
                                
                                # 显示统计信息
                                stats = data["statistics"]
                                print(f"📊 统计信息:")
                                print(f"  - 收款合计: {stats.get('total_income', 0)}")
                                print(f"  - 退款合计: {stats.get('total_refund', 0)}")
                                print(f"  - 到期收款合计: {stats.get('due_income', 0)}")
                                print(f"  - 逾期收款合计: {stats.get('overdue_income', 0)}")
                                print(f"  - 提前收款合计: {stats.get('early_income', 0)}")
                                print(f"  - 收款人数: {stats.get('total_customers', 0)}")
                                print(f"  - 收款订单数: {stats.get('total_orders', 0)}")
                                
                                # 显示列表信息
                                list_data = data["list"]
                                print(f"📋 列表信息:")
                                print(f"  - 总记录数: {list_data.get('total', 0)}")
                                print(f"  - 当前页: {list_data.get('page', 0)}")
                                print(f"  - 每页大小: {list_data.get('page_size', 0)}")
                                print(f"  - 当前页记录数: {len(list_data.get('data', []))}")
                                
                            else:
                                print("❌ 响应数据结构不正确，缺少statistics或list字段")
                        else:
                            print("❌ 响应中缺少data字段")
                    else:
                        print(f"❌ 接口返回错误: {result.get('message', '未知错误')}")
                else:
                    print("❌ 响应格式不正确，缺少code字段")
                    
            except json.JSONDecodeError as e:
                print(f"❌ JSON解析失败: {e}")
                print(f"原始响应: {response.text}")
        else:
            print(f"❌ HTTP请求失败: {response.status_code}")
            print(f"响应内容: {response.text}")
            
    except requests.exceptions.ConnectionError:
        print("❌ 连接失败，请确保服务器正在运行在 http://localhost:8108")
    except requests.exceptions.Timeout:
        print("❌ 请求超时")
    except Exception as e:
        print(f"❌ 请求异常: {e}")

if __name__ == "__main__":
    test_income_details_api()
