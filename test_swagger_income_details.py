#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
收入明细接口Swagger文档验证脚本
"""

import yaml
import json
import sys
import os

def validate_swagger_yaml():
    """验证Swagger YAML文件格式"""
    try:
        with open('vue/swagger_statistics_api.yaml', 'r', encoding='utf-8') as f:
            swagger_doc = yaml.safe_load(f)
        
        print("✅ YAML格式验证通过")
        return swagger_doc
    except yaml.YAMLError as e:
        print(f"❌ YAML格式错误: {e}")
        return None
    except Exception as e:
        print(f"❌ 文件读取错误: {e}")
        return None

def check_income_details_api(swagger_doc):
    """检查收入明细接口定义"""
    print("\n🔍 检查收入明细接口定义...")
    
    # 检查路径是否存在
    income_details_path = "/business/statistics/statisticscontroller/getIncomeDetails"
    if 'paths' not in swagger_doc:
        print("❌ 缺少paths定义")
        return False
    
    if income_details_path not in swagger_doc['paths']:
        print(f"❌ 缺少收入明细接口路径: {income_details_path}")
        return False
    
    print(f"✅ 找到收入明细接口路径: {income_details_path}")
    
    # 检查GET方法
    path_def = swagger_doc['paths'][income_details_path]
    if 'get' not in path_def:
        print("❌ 缺少GET方法定义")
        return False
    
    get_def = path_def['get']
    print("✅ 找到GET方法定义")
    
    # 检查基本属性
    required_fields = ['tags', 'summary', 'description', 'parameters', 'responses']
    for field in required_fields:
        if field not in get_def:
            print(f"❌ 缺少必需字段: {field}")
            return False
        print(f"✅ 找到字段: {field}")
    
    # 检查参数
    parameters = get_def['parameters']
    expected_params = [
        'order_no', 'user_name', 'mobile', 'fund_type', 'payment_method',
        'payment_status', 'payment_time_start', 'payment_time_end',
        'bill_time_start', 'bill_time_end', 'page', 'page_size'
    ]
    
    param_names = [p['name'] for p in parameters]
    for param in expected_params:
        if param not in param_names:
            print(f"❌ 缺少参数: {param}")
            return False
        print(f"✅ 找到参数: {param}")
    
    # 检查响应
    responses = get_def['responses']
    if '200' not in responses:
        print("❌ 缺少200响应定义")
        return False
    
    print("✅ 找到200响应定义")
    
    return True

def check_schemas(swagger_doc):
    """检查Schema定义"""
    print("\n🔍 检查Schema定义...")
    
    if 'components' not in swagger_doc or 'schemas' not in swagger_doc['components']:
        print("❌ 缺少components.schemas定义")
        return False
    
    schemas = swagger_doc['components']['schemas']
    
    # 检查收入明细相关的Schema
    required_schemas = [
        'IncomeStatistics',
        'IncomeDetailsItem',
        'IncomeDetailsPaginationResponse',
        'IncomeDetailsData',
        'IncomeDetailsResponse'
    ]
    
    for schema_name in required_schemas:
        if schema_name not in schemas:
            print(f"❌ 缺少Schema: {schema_name}")
            return False
        print(f"✅ 找到Schema: {schema_name}")
    
    # 检查IncomeStatistics的字段
    income_stats = schemas['IncomeStatistics']
    if 'properties' not in income_stats:
        print("❌ IncomeStatistics缺少properties")
        return False
    
    stats_fields = [
        'total_income', 'total_refund', 'due_income', 'overdue_income',
        'early_income', 'total_customers', 'total_orders'
    ]
    
    for field in stats_fields:
        if field not in income_stats['properties']:
            print(f"❌ IncomeStatistics缺少字段: {field}")
            return False
        print(f"✅ IncomeStatistics包含字段: {field}")
    
    return True

def check_examples(swagger_doc):
    """检查响应示例"""
    print("\n🔍 检查响应示例...")
    
    income_details_path = "/business/statistics/statisticscontroller/getIncomeDetails"
    get_def = swagger_doc['paths'][income_details_path]['get']
    
    if '200' not in get_def['responses']:
        print("❌ 缺少200响应")
        return False
    
    response_200 = get_def['responses']['200']
    if 'content' not in response_200:
        print("❌ 200响应缺少content")
        return False
    
    content = response_200['content']
    if 'application/json' not in content:
        print("❌ 缺少application/json content type")
        return False
    
    json_content = content['application/json']
    if 'examples' not in json_content:
        print("❌ 缺少响应示例")
        return False
    
    examples = json_content['examples']
    expected_examples = ['success', 'filtered', 'empty']
    
    for example_name in expected_examples:
        if example_name not in examples:
            print(f"❌ 缺少示例: {example_name}")
            return False
        print(f"✅ 找到示例: {example_name}")
    
    return True

def main():
    """主函数"""
    print("🚀 开始验证收入明细接口Swagger文档...")
    
    # 验证YAML格式
    swagger_doc = validate_swagger_yaml()
    if not swagger_doc:
        sys.exit(1)
    
    # 检查接口定义
    if not check_income_details_api(swagger_doc):
        print("\n❌ 收入明细接口定义检查失败")
        sys.exit(1)
    
    # 检查Schema定义
    if not check_schemas(swagger_doc):
        print("\n❌ Schema定义检查失败")
        sys.exit(1)
    
    # 检查响应示例
    if not check_examples(swagger_doc):
        print("\n❌ 响应示例检查失败")
        sys.exit(1)
    
    print("\n🎉 所有检查通过！收入明细接口Swagger文档验证成功！")
    
    # 输出统计信息
    print(f"\n📊 文档统计:")
    print(f"  - 总路径数: {len(swagger_doc.get('paths', {}))}")
    print(f"  - 总Schema数: {len(swagger_doc.get('components', {}).get('schemas', {}))}")
    print(f"  - 收入明细接口参数数: {len(swagger_doc['paths']['/business/statistics/statisticscontroller/getIncomeDetails']['get']['parameters'])}")

if __name__ == "__main__":
    main()
